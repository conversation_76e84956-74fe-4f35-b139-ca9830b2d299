# Cypridina

To start your Phoenix server:

* Run `mix setup` to install and setup dependencies
* Start Phoenix endpoint with `mix phx.server` or inside IEx with `iex -S mix phx.server`

Now you can visit [`localhost:4000`](http://localhost:4000) from your browser.

Ready to run in production? Please [check our deployment guides](https://hexdocs.pm/phoenix/deployment.html).

## Learn more

* Official website: https://www.phoenixframework.org/
* Guides: https://hexdocs.pm/phoenix/overview.html
* Docs: https://hexdocs.pm/phoenix
* Forum: https://elixirforum.com/c/phoenix-forum
* Source: https://github.com/phoenixframework/phoenix

####设计用户系统
请帮我为此项目设计一个通用的用户系统
1. 项目使用ash framework 3.5作为主要框架
2. 管理后台用backpex实现
3. 请帮我完善用户系统，以支持如下功能
    1. 代理系统，即玩家可以拉新，方式为登录管理后台后可创建账号，这些账号将成为玩家的下线，玩家可以设置对下线的投注买入卖出等行为进行抽水，抽水比例单独设置
4. 用backpex开发一个管理后台，以支持如下功能
    1. 配合代理系统，让代理可以在网页上创建账号，设置下线的抽水比例
    2. 控制未来某场比赛的名次分布
    3. 控制某场比赛的最终结果
    4. 查看所有玩家的投注记录
    5. 查看所有玩家的买入卖出记录
    7. 查看所有玩家的抽水记录
    8. 查看所有玩家的代理关系图
    9. 查看大盘实时持仓量和实时投注情况
    <!-- 2. 在ash_authentication的框架下，实现多渠道登录，支持微信登录、谷歌一键登录、手机验证码登录等 -->