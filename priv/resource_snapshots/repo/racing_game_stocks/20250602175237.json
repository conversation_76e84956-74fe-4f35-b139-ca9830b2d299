{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "user_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "racer_id", "type": "text"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "quantity", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "7CE599DBF7435A8ABE7B1148A916DEC22D2DC08D756C1AC3AA325D928EFF5963", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "racing_game_stocks_unique_user_stock_index", "keys": [{"type": "atom", "value": "user_id"}, {"type": "atom", "value": "racer_id"}], "name": "unique_user_stock", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "racing_game_stocks"}