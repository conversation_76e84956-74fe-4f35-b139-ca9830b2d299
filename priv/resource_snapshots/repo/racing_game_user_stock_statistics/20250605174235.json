{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "user_id", "type": "text"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "total_points", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "total_wins", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "total_losses", "type": "bigint"}, {"allow_nil?": false, "default": "\"0.0\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "win_rate", "type": "decimal"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "biggest_win", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "biggest_loss", "type": "bigint"}, {"allow_nil?": false, "default": "\"0.0\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "average_bet", "type": "decimal"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "total_races", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "profit_loss", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "04524A7039A8D7FC45BDA20F80917EBB7523D8509AD32E7C7BE83B21AD51AFBD", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "racing_game_user_stock_statistics_unique_user_stats_index", "keys": [{"type": "atom", "value": "user_id"}], "name": "unique_user_stats", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "racing_game_user_stock_statistics"}