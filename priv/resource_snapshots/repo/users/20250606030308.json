{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "numeric_id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "username", "type": "citext"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "client_uniq_id", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "email", "type": "citext"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "hashed_password", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "confirmed_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "-1", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "agent_level", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "users_created_by_agent_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "created_by_agent", "type": "uuid"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "permission_level", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "065F242F0D6D8843D4AE91D6E78EA30E638F7CB72DDB38BBF255D92348A2AFA9", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "users_unique_email_index", "keys": [{"type": "atom", "value": "email"}], "name": "unique_email", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "users_unique_numeric_id_index", "keys": [{"type": "atom", "value": "numeric_id"}], "name": "unique_numeric_id", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "users_unique_username_index", "keys": [{"type": "atom", "value": "username"}], "name": "unique_username", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "users_unique_client_uniq_id_index", "keys": [{"type": "atom", "value": "client_uniq_id"}], "name": "unique_client_uniq_id", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "users"}