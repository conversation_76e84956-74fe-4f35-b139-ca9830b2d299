{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "racer_id", "type": "text"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "total_quantity", "type": "bigint"}, {"allow_nil?": false, "default": "\"0\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "total_cost", "type": "decimal"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "CBD0E41BA402A34EB52F4238A7A468551807C7031901144A7999E487001B48F6", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "racing_game_stock_statistics_unique_racer_stats_index", "keys": [{"type": "atom", "value": "racer_id"}], "name": "unique_racer_stats", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "racing_game_stock_statistics"}