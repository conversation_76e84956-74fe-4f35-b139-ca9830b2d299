{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "agent_relationships_agent_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "agent_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "agent_relationships_subordinate_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "subordinate_id", "type": "uuid"}, {"allow_nil?": false, "default": "1", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "level", "type": "bigint"}, {"allow_nil?": false, "default": "\"0.05\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "commission_rate", "type": "decimal"}, {"allow_nil?": false, "default": "1", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "status", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "9342A4A3D36D2208A075712368F0B91F59D3C9BA3426EF5E3BC6A104B0D7E6EA", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "agent_relationships_unique_agent_subordinate_index", "keys": [{"type": "atom", "value": "agent_id"}, {"type": "atom", "value": "subordinate_id"}], "name": "unique_agent_subordinate", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "agent_relationships"}