{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "user_assets_user_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "user_id", "type": "uuid"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "points", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "commission", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "09652F21D41EFDCC948FD3FA17408C6236CF8E1F33C4FA493F77FABB2D499D9D", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "user_assets_unique_user_id_index", "keys": [{"type": "atom", "value": "user_id"}], "name": "unique_user_id", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "user_assets"}