{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "issue", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "issue_id", "type": "text"}, {"allow_nil?": true, "default": "[]", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "positions", "type": ["array", "text"]}, {"allow_nil?": true, "default": "[]", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "speeds", "type": ["array", "text"]}, {"allow_nil?": true, "default": "[]", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "end_times", "type": ["array", "text"]}, {"allow_nil?": true, "default": "0", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "status", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "start_time", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "end_time", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "issue_end_time", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "order_end_time", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "bet_amount_map", "type": "map"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "8C4EAD5A67C1FF4D733DF79C15F5A6F6365BBD398F2749E6F233110DC9B3052E", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "races"}