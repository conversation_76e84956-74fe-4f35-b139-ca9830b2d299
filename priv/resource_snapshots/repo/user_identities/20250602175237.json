{"attributes": [{"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "refresh_token", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "access_token_expires_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "access_token", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "uid", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "strategy", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "user_identities_user_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "user_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "78D6D9C30632586074E7376DD8FEAA4DF09315C3D76EDD0AE170C721C2BB9719", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "user_identities_unique_on_strategy_and_uid_and_user_id_index", "keys": [{"type": "atom", "value": "strategy"}, {"type": "atom", "value": "uid"}, {"type": "atom", "value": "user_id"}], "name": "unique_on_strategy_and_uid_and_user_id", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "user_identities"}