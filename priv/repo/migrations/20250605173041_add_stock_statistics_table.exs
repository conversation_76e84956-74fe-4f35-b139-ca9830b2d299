defmodule Cypridina.Repo.Migrations.AddStockStatisticsTable do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:racing_game_stock_statistics) do
      remove :total_quantity
      add :total_bought, :bigint, null: false, default: 0
      add :total_sold, :bigint, null: false, default: 0
      add :total_revenue, :decimal, null: false, default: "0"
    end
  end

  def down do
    alter table(:racing_game_stock_statistics) do
      remove :total_revenue
      remove :total_sold
      remove :total_bought
      add :total_quantity, :bigint, null: false, default: 0
    end
  end
end
