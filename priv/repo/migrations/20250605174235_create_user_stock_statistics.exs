defmodule Cypridina.Repo.Migrations.CreateUserStockStatistics do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:racing_game_user_stock_statistics, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :text, null: false
      add :total_points, :bigint, null: false, default: 0
      add :total_wins, :bigint, null: false, default: 0
      add :total_losses, :bigint, null: false, default: 0
      add :win_rate, :decimal, null: false, default: "0.0"
      add :biggest_win, :bigint, null: false, default: 0
      add :biggest_loss, :bigint, null: false, default: 0
      add :average_bet, :decimal, null: false, default: "0.0"
      add :total_races, :bigint, null: false, default: 0
      add :profit_loss, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:racing_game_user_stock_statistics, [:user_id],
             name: "racing_game_user_stock_statistics_unique_user_stats_index"
           )
  end

  def down do
    drop_if_exists unique_index(:racing_game_user_stock_statistics, [:user_id],
                     name: "racing_game_user_stock_statistics_unique_user_stats_index"
                   )

    drop table(:racing_game_user_stock_statistics)
  end
end
