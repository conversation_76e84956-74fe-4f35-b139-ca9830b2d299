defmodule Cypridina.Repo.Migrations.AddPointsTransaction do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:racing_game_points_transactions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :transaction_id, :text, null: false
      add :user_id, :uuid, null: false
      add :transaction_type, :text, null: false
      add :amount, :decimal, null: false
      add :balance_before, :decimal, null: false
      add :balance_after, :decimal, null: false
      add :race_issue, :text
      add :description, :text
      add :extra_data, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end
  end

  def down do
    drop table(:racing_game_points_transactions)
  end
end
