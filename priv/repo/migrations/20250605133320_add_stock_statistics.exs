defmodule Cypridina.Repo.Migrations.AddStockStatistics do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:racing_game_stock_statistics, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :racer_id, :text, null: false
      add :total_quantity, :bigint, null: false, default: 0
      add :total_cost, :decimal, null: false, default: "0"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:racing_game_stock_statistics, [:racer_id],
             name: "racing_game_stock_statistics_unique_racer_stats_index"
           )
  end

  def down do
    drop_if_exists unique_index(:racing_game_stock_statistics, [:racer_id],
                     name: "racing_game_stock_statistics_unique_racer_stats_index"
                   )

    drop table(:racing_game_stock_statistics)
  end
end
