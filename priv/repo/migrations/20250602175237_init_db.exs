defmodule Cypridina.Repo.Migrations.InitDb do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:users, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :numeric_id, :bigint, null: false
      add :username, :citext
      add :email, :citext
      add :hashed_password, :text, null: false
      add :confirmed_at, :utc_datetime_usec
      add :agent_level, :bigint, null: false, default: -1

      add :created_by_agent,
          references(:users,
            column: :id,
            name: "users_created_by_agent_fkey",
            type: :uuid,
            prefix: "public"
          )

      add :permission_level, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:users, [:email], name: "users_unique_email_index")

    create unique_index(:users, [:numeric_id], name: "users_unique_numeric_id_index")

    create unique_index(:users, [:username], name: "users_unique_username_index")

    create table(:user_identities, primary_key: false) do
      add :refresh_token, :text
      add :access_token_expires_at, :utc_datetime_usec
      add :access_token, :text
      add :uid, :text, null: false
      add :strategy, :text, null: false
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :user_id,
          references(:users,
            column: :id,
            name: "user_identities_user_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    create unique_index(:user_identities, [:strategy, :uid, :user_id],
             name: "user_identities_unique_on_strategy_and_uid_and_user_id_index"
           )

    create table(:user_assets, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :user_id,
          references(:users,
            column: :id,
            name: "user_assets_user_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :points, :bigint, null: false, default: 0
      add :commission, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:user_assets, [:user_id], name: "user_assets_unique_user_id_index")

    create table(:tokens, primary_key: false) do
      add :jti, :text, null: false, primary_key: true
      add :subject, :text, null: false
      add :expires_at, :utc_datetime, null: false
      add :purpose, :text, null: false
      add :extra_data, :map

      add :created_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:racing_game_stocks, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :racer_id, :text, null: false
      add :quantity, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:racing_game_stocks, [:user_id, :racer_id],
             name: "racing_game_stocks_unique_user_stock_index"
           )

    create table(:racing_game_bets, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :race_issue, :text, null: false
      add :selection, :text, null: false
      add :amount, :bigint, null: false
      add :status, :bigint, null: false, default: 0
      add :payout, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:races, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :issue, :text, null: false
      add :issue_id, :text
      add :positions, {:array, :text}, default: []
      add :speeds, {:array, :text}, default: []
      add :end_times, {:array, :text}, default: []
      add :status, :bigint, default: 0
      add :start_time, :utc_datetime_usec
      add :end_time, :utc_datetime_usec
      add :issue_end_time, :utc_datetime_usec
      add :order_end_time, :utc_datetime_usec
      add :bet_amount_map, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:racers, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :number, :bigint, null: false
      add :code, :text, null: false
      add :name, :text, null: false
      add :total_wins, :bigint
      add :current_odds, :decimal

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:leaderboards, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :racer_number, :bigint, null: false
      add :win_count, :bigint

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:commission_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :agent_id,
          references(:users,
            column: :id,
            name: "commission_records_agent_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :subordinate_id,
          references(:users,
            column: :id,
            name: "commission_records_subordinate_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :transaction_type, :text, null: false
      add :transaction_id, :uuid, null: false
      add :original_amount, :bigint, null: false
      add :commission_rate, :decimal, null: false
      add :commission_amount, :bigint, null: false
      add :status, :bigint, null: false, default: 1

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:api_keys, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :api_key_hash, :binary, null: false
      add :expires_at, :utc_datetime_usec, null: false

      add :user_id,
          references(:users,
            column: :id,
            name: "api_keys_user_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    create unique_index(:api_keys, [:api_key_hash], name: "api_keys_unique_api_key_index")

    create table(:agent_relationships, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :agent_id,
          references(:users,
            column: :id,
            name: "agent_relationships_agent_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :subordinate_id,
          references(:users,
            column: :id,
            name: "agent_relationships_subordinate_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :level, :bigint, null: false, default: 1
      add :commission_rate, :decimal, null: false, default: "0.05"
      add :status, :bigint, null: false, default: 1

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:agent_relationships, [:agent_id, :subordinate_id],
             name: "agent_relationships_unique_agent_subordinate_index"
           )
  end

  def down do
    drop_if_exists unique_index(:agent_relationships, [:agent_id, :subordinate_id],
                     name: "agent_relationships_unique_agent_subordinate_index"
                   )

    drop constraint(:agent_relationships, "agent_relationships_agent_id_fkey")

    drop constraint(:agent_relationships, "agent_relationships_subordinate_id_fkey")

    drop table(:agent_relationships)

    drop_if_exists unique_index(:api_keys, [:api_key_hash], name: "api_keys_unique_api_key_index")

    drop constraint(:api_keys, "api_keys_user_id_fkey")

    drop table(:api_keys)

    drop constraint(:commission_records, "commission_records_agent_id_fkey")

    drop constraint(:commission_records, "commission_records_subordinate_id_fkey")

    drop table(:commission_records)

    drop table(:leaderboards)

    drop table(:racers)

    drop table(:races)

    drop table(:racing_game_bets)

    drop_if_exists unique_index(:racing_game_stocks, [:user_id, :racer_id],
                     name: "racing_game_stocks_unique_user_stock_index"
                   )

    drop table(:racing_game_stocks)

    drop table(:tokens)

    drop_if_exists unique_index(:user_assets, [:user_id],
                     name: "user_assets_unique_user_id_index"
                   )

    drop constraint(:user_assets, "user_assets_user_id_fkey")

    drop table(:user_assets)

    drop_if_exists unique_index(:user_identities, [:strategy, :uid, :user_id],
                     name: "user_identities_unique_on_strategy_and_uid_and_user_id_index"
                   )

    drop constraint(:user_identities, "user_identities_user_id_fkey")

    drop table(:user_identities)

    drop_if_exists unique_index(:users, [:username], name: "users_unique_username_index")

    drop_if_exists unique_index(:users, [:numeric_id], name: "users_unique_numeric_id_index")

    drop_if_exists unique_index(:users, [:email], name: "users_unique_email_index")

    drop constraint(:users, "users_created_by_agent_fkey")

    drop table(:users)
  end
end
