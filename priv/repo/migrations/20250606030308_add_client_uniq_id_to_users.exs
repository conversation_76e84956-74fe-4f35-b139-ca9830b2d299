defmodule Cypridina.Repo.Migrations.AddClientUniqIdToUsers do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:users) do
      add :client_uniq_id, :text
    end

    create unique_index(:users, [:client_uniq_id], name: "users_unique_client_uniq_id_index")
  end

  def down do
    drop_if_exists unique_index(:users, [:client_uniq_id],
                     name: "users_unique_client_uniq_id_index"
                   )

    alter table(:users) do
      remove :client_uniq_id
    end
  end
end
