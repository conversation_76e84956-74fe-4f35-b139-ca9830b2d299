defmodule Cypridina.Repo.Migrations.AddStatusToPointsTransactions do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:racing_game_points_transactions) do
      add :status, :text, null: false, default: "confirmed"
    end
  end

  def down do
    alter table(:racing_game_points_transactions) do
      remove :status
    end
  end
end
