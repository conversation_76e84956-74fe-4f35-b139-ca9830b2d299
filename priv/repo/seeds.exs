# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     Cypridina.Repo.insert!(%Cypridina.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.
require Logger
alias Cypridina.Accounts.UserManagementService

Logger.info("开始初始化数据库...")

# 初始化超级管理员
Logger.info("=== 初始化超级管理员 ===")

case UserManagementService.initialize_super_admin(%{
       username: "super_admin",
       password: "admin123456",
       password_confirmation: "admin123456"
     }) do
  {:ok, super_admin} ->
    Logger.info("✅ 超级管理员初始化成功: #{super_admin.username} (ID: #{super_admin.numeric_id})")

  {:error, error} ->
    Logger.error("❌ 超级管理员初始化失败: #{inspect(error)}")
end

Logger.info("✅ 数据库初始化完成！")

# 显示创建的账号信息
Logger.info("""

=== 创建的账号信息 ===
超级管理员: super_admin / admin123456

请妥善保管这些账号信息！
其他管理员和用户可以通过管理界面或Mix任务创建。
""")
