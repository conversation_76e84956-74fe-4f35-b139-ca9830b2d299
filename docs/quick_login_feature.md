# 快速登录功能文档

## 概述

快速登录功能允许玩家通过提供客户端唯一标识（client_uniq_id）来快速创建游客账户并登录游戏。这个功能特别适用于测试阶段，让玩家可以无需注册就能快速体验游戏。

## 功能特性

- **无需注册**：玩家只需提供客户端唯一标识即可登录
- **自动创建游客账户**：系统自动为新设备创建游客账户
- **初始积分赠送**：新游客账户自动获得50000积分
- **设备绑定**：同一设备再次登录时返回相同的游客账户

## 技术实现

### 数据库变更

使用 `mix ash.codegen` 生成了数据库迁移文件，为用户表添加了 `client_uniq_id` 字段：

```sql
ALTER TABLE users ADD COLUMN client_uniq_id TEXT;
CREATE UNIQUE INDEX users_client_uniq_id_index ON users (client_uniq_id);
```

### 核心模块

#### 1. User 资源更新 (`lib/cypridina/accounts/resources/user.ex`)

- 添加了 `client_uniq_id` 属性
- 新增 `create_guest_user` action
- 添加了通过 `client_uniq_id` 查询用户的方法
- 实现了游客用户名自动生成（格式：Guest{数字ID}）
- 集成了用户资产初始化（50000积分）

#### 2. 快速登录处理器 (`lib/cypridina/protocol/quick_login_handler.ex`)

- `handle_quick_login/1`：主要处理函数
- 检查用户是否存在，不存在则创建新游客账户
- 错误处理和日志记录

#### 3. WebSocket 处理器更新 (`lib/teen/protocol/websocket_handler.ex`)

- 修改登录逻辑以支持快速登录（accounttype = 3）
- 集成快速登录处理器
- 构建适当的登录响应

## 使用方法

### 客户端请求格式

```json
{
  "mainId": 1,
  "subId": 1,
  "data": {
    "account": "",
    "password": "",
    "accounttype": 3,
    "uniquecode": "device_unique_identifier_123",
    "siteid": 1,
    "promotionid": 0
  }
}
```

### 关键参数

- `accounttype`: 设置为 `3` 表示快速登录
- `uniquecode`: 客户端唯一标识，如设备ID、UUID等

### 服务器响应

成功登录时，服务器返回包含用户信息和初始积分的完整响应。

## 测试

创建了基本的单元测试 (`test/cypridina/protocol/quick_login_handler_test.exs`) 来验证：

- 空的客户端唯一标识处理
- 错误情况处理

## 安全考虑

- 客户端唯一标识应该是真正唯一的（如设备UUID）
- 游客账户密码使用随机生成的哈希值
- 数据库层面确保 `client_uniq_id` 的唯一性

## 部署说明

1. 运行数据库迁移：`mix ecto.migrate`
2. 重启应用服务器
3. 客户端可以开始使用快速登录功能

## 未来改进

- 添加游客账户转正式账户的功能
- 实现游客账户的过期机制
- 添加更详细的使用统计和监控
- 支持游客账户数据迁移
