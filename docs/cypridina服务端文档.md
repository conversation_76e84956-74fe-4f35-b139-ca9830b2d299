<!-- 参考https://hexdocs.pm/phoenix/up_and_running.html -->

<!-- 在本地安装hex包管理器 -->
###=# 一些常用的mix命令
HEX_MIRROR=http://hexpm.upyun.com mix local.hex
<!-- 在本地安装erlang用的工具rebar -->
HEX_MIRROR=http://hexpm.upyun.com mix local.rebar
 <!--安装依赖  -->
HEX_MIRROR=http://hexpm.upyun.com mix deps.get


#### 初始化项目
ash mix任务见https://hexdocs.pm/ash/Mix.Tasks.Ash.Codegen.html

```bash
<!-- 强制清库 -->
mix ecto.drop  --force-drop
<!-- 根据ash的资源声明，生成迁移文件 -->
mix ash.codegen <xxxx-随便起个名>
<!-- 初始化项目 -->
mix setup
```

#### 启动服务
```bash
<!-- 赋名式启动 -->
MIX_ENV=dev elixir --sname dev_server -S mix phx.server
<!-- 连接到上述节点 -->
iex --remsh dev_server --sname dev

<!-- 正式服 -->
MIX_ENV=prod PORT=4001 elixir --sname prod_server -S mix phx.server
iex --remsh prod_server --sname dev
```


### 用户系统
#### 登录
1.  用户输入手机号，向服务端请求手机验证码
2.  服务端回复OK，并向阿里云请求向该用户发送sdk
3.  用户收到验证码，将验证码和手机号一起发送给服务器
4.  服务器认可该手机号为用户所有，并回复该用户的token
    1.  该token为jwt
5. 使用该token连接websocket
6. 请求用户加入的广场
7.  如广场列表为空，则请求附近的广场并显示到推荐页
    1.  用户选择一个广场，调用加入广场接口，并请求广场详情接口
    2.  用户调用加入房间协议，加入默认房间
    3.  用户通过协议请求该房间近期消息
    4.  用户发送消息
    5.  用户接收其他玩家发来的消息
    6.  用户退出房间
    7.  用户加入语音房间
    8.  用户发送语音流式数据
    9.  用户接收其他玩家发来的语音数据
8.  用户点击私信页，请求未读消息
    1.  用户给其他玩家发送私信
    2.  用户接收其他玩家发来的私信

#### 即时通讯
1.  协议
   1. 发送私信 send_priv_msg
      1. role_id
      2. content
      3. date:null
   2. 传输流式数据（语音视频）upload_stream
      1. role_id
      2. content
      3. date:null
   3. 收房间消息 receive_room_msg
   4. 收私信 receive_priv_msg
#### 社交


#### api
##### 登录登出
1. 请求手机验证码
2. 通过手机验证码获取token(login)
3. 销毁token(logout)

##### 消息
1. 发送消息
2. 接收消息
3. 历史消息
4. 转发消息
5. 消息变更
6. 插入消息（本地）
7. 消息回应（为帖子功能开发）
8. 消息免打扰（暂不开发）
9. 消息翻译（暂不开发）
10. 语音转文字（暂不开发）

##### 通话
1. 语音聊天
2. 视频聊天
3. 直播
   
##### 离线推送
1. （暂不开发）

##### 本地搜索
1. 搜索消息（暂不开发）
2. 搜索好友（暂不开发）
3. 搜索群组（暂不开发）
4. 搜索群成员（暂不开发）

##### 服务器搜索
1. 搜索消息

##### 邮件系统
1. 数据库设计
```bash
mix phx.gen.json G1 Mail mails \
    user_id:references:user_game_datas \
    content:string \
    category:integer \ 
    expire_at:integer \ 
    props:array:map 
```

8.134.134.198
QDd5@#$QWE_%123@YS
8492
admin
ac59075b964b0715
http://8.134.134.198:8082/hbiInterface
Chao1yanShi
qwerty@+--123
kaifaceshi12356q
LTAI5tL2MeQqLMTb2hS3o1jJ
******************************

你是一名资深elixir程序员，你的任务是在不修改客户端代码（路径IndiaGameClient/assets/games/longhu）的前提下，将一个由c++实现的龙虎斗游戏的服务端（路径IndiaGameServer/Code/Game/LongHu），完整移植到elixir上

1. 下注看不见
3. 没有玩家列表
6. 客户端下注请求处理

2. 没有历史记录
4. 上庄
5. 真实积分

已处理
7. 亮牌时牌面缺失
8. 进入房间时发送房间详情