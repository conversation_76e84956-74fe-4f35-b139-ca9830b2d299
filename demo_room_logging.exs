#!/usr/bin/env elixir

# 房间日志过滤器演示脚本
# 运行方式: elixir demo_room_logging.exs

Mix.install([])

defmodule DemoRoomLogging do
  @moduledoc """
  演示房间日志过滤器的功能
  """

  require Logger

  def run do
    IO.puts("🏠 房间日志过滤器演示")
    IO.puts("=" |> String.duplicate(50))

    # 模拟创建房间日志过滤器
    room_id = "demo_room_#{System.unique_integer([:positive])}"

    IO.puts("1. 创建房间: #{room_id}")

    # 设置Logger metadata
    Logger.metadata(room_id: room_id)

    IO.puts("2. 设置Logger metadata: room_id = #{room_id}")

    # 模拟房间日志
    Logger.info("房间初始化完成")
    Logger.info("玩家 user_123 加入房间")
    Logger.info("游戏开始")
    Logger.warning("玩家 user_456 连接超时")
    Logger.info("游戏结束，清理房间资源")

    IO.puts("3. 生成了一些房间日志")

    # 清理metadata
    Logger.metadata(room_id: nil)

    IO.puts("4. 清理Logger metadata")

    # 模拟系统日志（不应该被房间过滤器捕获）
    Logger.info("系统日志：应用正常运行")

    IO.puts("5. 生成系统日志")

    IO.puts("\n✅ 演示完成！")
    IO.puts("📁 房间日志应该被写入到: logs/rooms/room_#{room_id}.log")
    IO.puts("📁 系统日志应该被写入到: logs/system.log")
  end
end

DemoRoomLogging.run()
