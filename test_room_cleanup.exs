#!/usr/bin/env elixir

# 测试房间清理功能的脚本
defmodule TestRoomCleanup do
  @moduledoc """
  测试房间终止事件广播和Registry.lookup失败时的清理功能
  """

  require Logger

  def run do
    Logger.info("🧪 开始测试房间清理功能...")

    # 启动必要的应用
    setup_test_environment()

    # 测试1: 房间终止事件处理
    test_room_termination_event()

    # 测试2: Registry.lookup失败时的清理
    test_registry_lookup_failure()

    Logger.info("🧪 测试完成!")
  end

  defp setup_test_environment do
    Logger.info("🔧 设置测试环境...")

    # 确保PubSub和Registry启动
    {:ok, _} = Phoenix.PubSub.start_link(name: Cypridina.PubSub)
    {:ok, _} = Registry.start_link(keys: :unique, name: :game_room_registry)

    # 启动RoomManager
    {:ok, _} = Cypridina.Teen.GameSystem.RoomManager.start_link([])

    Process.sleep(100)  # 给系统时间启动
  end

  defp test_room_termination_event do
    Logger.info("🧪 测试1: 房间终止事件处理")

    # 创建一个测试房间
    {:ok, room_id} = Cypridina.Teen.GameSystem.RoomManager.create_room(:teen_patti, "test_user", %{})
    Logger.info("创建测试房间: #{room_id}")

    # 检查房间存在
    assert Cypridina.Teen.GameSystem.RoomManager.room_exists?(room_id), "房间应该存在"

    # 获取房间统计
    stats_before = Cypridina.Teen.GameSystem.RoomManager.get_room_stats()
    Logger.info("终止前统计: #{inspect(stats_before)}")

    # 销毁房间 - 这应该触发房间终止事件
    :ok = Cypridina.Teen.GameSystem.RoomManager.destroy_room(room_id)

    # 等待事件处理
    Process.sleep(200)

    # 检查房间是否被清理
    refute Cypridina.Teen.GameSystem.RoomManager.room_exists?(room_id), "房间应该已被清理"

    # 获取房间统计
    stats_after = Cypridina.Teen.GameSystem.RoomManager.get_room_stats()
    Logger.info("终止后统计: #{inspect(stats_after)}")

    Logger.info("✅ 测试1通过: 房间终止事件处理正常")
  end

  defp test_registry_lookup_failure do
    Logger.info("🧪 测试2: Registry.lookup失败时的清理")

    # 创建一个房间
    {:ok, room_id} = Cypridina.Teen.GameSystem.RoomManager.create_room(:teen_patti, "test_user2", %{})
    Logger.info("创建测试房间: #{room_id}")

    # 检查房间存在
    assert Cypridina.Teen.GameSystem.RoomManager.room_exists?(room_id), "房间应该存在"

    # 手动停止房间进程（模拟异常终止）
    case Registry.lookup(:game_room_registry, room_id) do
      [{pid, _}] ->
        Process.exit(pid, :kill)
        Logger.info("手动终止房间进程: #{inspect(pid)}")
      [] ->
        Logger.warning("找不到房间进程")
    end

    # 等待进程终止
    Process.sleep(100)

    # 尝试向房间发送消息 - 这应该触发清理
    result = Cypridina.Teen.GameSystem.RoomManager.send_to_room(room_id, :test_message)
    Logger.info("发送消息结果: #{inspect(result)}")

    # 等待清理处理
    Process.sleep(200)

    # 检查房间状态 - 第二次调用应该返回false并触发清理
    exists = Cypridina.Teen.GameSystem.RoomManager.room_exists?(room_id)
    Logger.info("房间存在检查: #{exists}")

    # 获取最终统计
    final_stats = Cypridina.Teen.GameSystem.RoomManager.get_room_stats()
    Logger.info("最终统计: #{inspect(final_stats)}")

    Logger.info("✅ 测试2通过: Registry.lookup失败清理正常")
  end

  # 简单的断言辅助函数
  defp assert(condition, message) do
    unless condition do
      raise "断言失败: #{message}"
    end
  end

  defp refute(condition, message) do
    if condition do
      raise "断言失败: #{message}"
    end
  end
end

# 运行测试
TestRoomCleanup.run()
