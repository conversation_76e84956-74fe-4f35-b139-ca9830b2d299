# 房间日志过滤器系统

## 概述

本系统为每个房间创建独立的日志文件，根据Logger metadata中的`room_id`自动分离房间日志和系统日志。

## 功能特性

- ✅ **房间独立日志**: 每个房间有自己的日志文件
- ✅ **自动过滤**: 根据`room_id` metadata自动分离日志
- ✅ **动态管理**: 房间创建时添加过滤器，房间结束时自动清理
- ✅ **全局过滤**: 系统日志排除房间相关日志
- ✅ **日志轮转**: 支持日志文件大小限制和压缩

## 文件结构

```
lib/teen/game_system/
├── room_base.ex              # 房间基础模块（已修改）
└── room_log_filter.ex        # 房间日志过滤器（新增）

config/
└── config.exs                # 日志配置（已修改）

lib/cypridina/
└── application.ex            # 应用启动（已修改）

test/teen/game_system/
└── room_log_filter_test.exs  # 测试文件（新增）
```

## 核心组件

### 1. RoomLogFilter 模块

负责动态管理房间日志handler：

- `create_room_logger/1` - 为房间创建专用日志handler
- `remove_room_logger/1` - 删除房间日志handler
- `room_log_filter/2` - 房间日志过滤函数
- `global_log_filter/2` - 全局日志过滤函数

### 2. RoomBase 集成

在房间生命周期中自动管理日志过滤器：

- 房间初始化时创建日志过滤器
- 设置Logger metadata包含`room_id`
- 房间终止时清理日志过滤器

### 3. 全局日志配置

- 添加`room_id`到Logger metadata
- 在应用启动时设置全局过滤器

## 日志文件组织

```
logs/
├── system.log              # 系统日志（排除房间日志）
└── rooms/
    ├── room_123.log        # 房间123的日志
    ├── room_456.log        # 房间456的日志
    └── ...
```

## 使用方式

### 在房间代码中记录日志

```elixir
# 房间初始化时会自动设置Logger metadata
Logger.metadata(room_id: state.id)

# 之后的所有日志都会被自动分离到房间专用文件
Logger.info("玩家加入房间")
Logger.warning("游戏状态异常")
Logger.error("房间处理错误")
```

### 系统日志

```elixir
# 没有room_id metadata的日志会写入系统日志
Logger.info("应用启动完成")
Logger.error("数据库连接失败")
```

## 配置选项

### 房间日志配置

- **文件大小限制**: 5MB per room log
- **文件数量**: 最多保留3个历史文件
- **压缩**: 自动压缩轮转的日志文件
- **同步间隔**: 5秒

### 日志格式

房间日志格式：
```
2024-01-15 10:30:45 [info] [ROOM:123] 玩家加入房间
```

系统日志格式：
```
2024-01-15 10:30:45 [info] 应用启动完成
```

## 测试

运行测试：
```bash
mix test test/teen/game_system/room_log_filter_test.exs
```

运行演示：
```bash
elixir demo_room_logging.exs
```

## 性能考虑

- 日志过滤器使用高效的pattern matching
- 动态handler管理避免内存泄漏
- 日志文件轮转防止磁盘空间耗尽
- 异步日志写入不影响房间性能

## 故障处理

- 如果创建日志过滤器失败，房间仍可正常运行
- 日志目录会自动创建
- 房间终止时确保清理日志过滤器
- 支持优雅的错误恢复

## 监控和维护

- 日志文件自动轮转和压缩
- 可通过Logger配置调整日志级别
- 支持运行时动态调整日志配置
- 提供日志统计和监控接口
