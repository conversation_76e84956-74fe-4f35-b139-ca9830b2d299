# 实际运行时测试房间清理系统
# 这个脚本会在实际的Phoenix环境中测试房间清理机制

# 首先检查环境
Application.ensure_all_started(:cypridina)

Logger.configure(level: :info)
Logger.info("🧪 [RUNTIME_TEST] 开始房间清理系统运行时测试")

# 等待应用启动
Process.sleep(1000)

# 测试房间管理器是否正常启动
room_manager_pid = GenServer.whereis(Cypridina.Teen.GameSystem.RoomManager)
if room_manager_pid do
  Logger.info("✅ [RUNTIME_TEST] RoomManager 进程已启动: #{inspect(room_manager_pid)}")
else
  Logger.error("❌ [RUNTIME_TEST] RoomManager 进程未启动")
  System.halt(1)
end

# 测试Registry是否存在
registry_name = :game_room_registry
case Registry.lookup(registry_name, "test") do
  [] ->
    Logger.info("✅ [RUNTIME_TEST] Registry #{registry_name} 正常工作")
  _ ->
    Logger.info("✅ [RUNTIME_TEST] Registry #{registry_name} 正常工作")
end

# 测试房间创建和清理
Logger.info("🎯 [RUNTIME_TEST] 测试房间创建...")

# 尝试匹配一个房间 (Teen Patti游戏)
test_user_id = "test_user_#{:rand.uniform(10000)}"
match_result = GenServer.call(room_manager_pid, {:match_room, test_user_id, 1, %{}})

case match_result do
  {:ok, room_info} ->
    room_id = room_info.room_id
    Logger.info("✅ [RUNTIME_TEST] 房间创建成功: #{room_id}")

    # 测试房间是否真实存在
    case Registry.lookup(registry_name, room_id) do
      [{pid, _}] ->
        Logger.info("✅ [RUNTIME_TEST] 房间进程存在: #{inspect(pid)}")

        # 测试发送消息到房间
        send_result = Cypridina.Teen.GameSystem.RoomManager.send_to_room(room_id, :test_message)
        Logger.info("📤 [RUNTIME_TEST] 发送消息到房间结果: #{inspect(send_result)}")

        # 测试调用房间方法
        call_result = Cypridina.Teen.GameSystem.RoomManager.call_room(room_id, :get_state)
        Logger.info("📞 [RUNTIME_TEST] 调用房间方法结果: #{inspect(call_result)}")

        # 测试房间存在检查
        exists_result = Cypridina.Teen.GameSystem.RoomManager.room_exists?(room_id)
        Logger.info("🔍 [RUNTIME_TEST] 房间存在检查结果: #{exists_result}")

        # 强制终止房间进程来测试清理机制
        Logger.info("💥 [RUNTIME_TEST] 强制终止房间进程测试清理机制...")
        Process.exit(pid, :kill)

        # 等待终止和清理
        Process.sleep(500)

        # 测试Registry.lookup失败处理
        Logger.info("🧹 [RUNTIME_TEST] 测试Registry.lookup失败处理...")

        # 尝试再次发送消息（应该触发清理）
        send_result_after = Cypridina.Teen.GameSystem.RoomManager.send_to_room(room_id, :test_message)
        Logger.info("📤 [RUNTIME_TEST] 房间终止后发送消息结果: #{inspect(send_result_after)}")

        # 尝试再次调用房间方法（应该触发清理）
        call_result_after = Cypridina.Teen.GameSystem.RoomManager.call_room(room_id, :get_state)
        Logger.info("📞 [RUNTIME_TEST] 房间终止后调用方法结果: #{inspect(call_result_after)}")

        # 检查房间是否存在（应该触发清理）
        exists_result_after = Cypridina.Teen.GameSystem.RoomManager.room_exists?(room_id)
        Logger.info("🔍 [RUNTIME_TEST] 房间终止后存在检查结果: #{exists_result_after}")

        # 等待异步清理完成
        Process.sleep(1000)

        # 检查房间管理器的状态
        stats = GenServer.call(room_manager_pid, :get_stats)
        Logger.info("📊 [RUNTIME_TEST] 清理后房间管理器统计: #{inspect(stats)}")

      [] ->
        Logger.error("❌ [RUNTIME_TEST] 房间进程未在Registry中找到")
    end

  {:error, reason} ->
    Logger.error("❌ [RUNTIME_TEST] 房间创建失败: #{inspect(reason)}")
end

# 测试陈旧房间数据清理
Logger.info("🧹 [RUNTIME_TEST] 测试手动清理陈旧房间数据...")
fake_room_id = "fake_room_#{:rand.uniform(10000)}"
GenServer.cast(room_manager_pid, {:cleanup_stale_room, fake_room_id})

Process.sleep(500)

Logger.info("""
📋 [RUNTIME_TEST] 房间清理系统运行时测试完成

✅ 测试项目:
  - RoomManager进程启动检查
  - Registry正常工作检查
  - 房间创建和匹配
  - 房间进程通信
  - 强制房间终止
  - Registry.lookup失败处理
  - 异步清理机制
  - 手动清理触发

🎯 关键功能验证:
  - send_to_room在房间不存在时返回{:error, :room_not_found}
  - call_room在房间不存在时返回{:error, :room_not_found}
  - room_exists?在房间不存在时返回false
  - 所有失败情况都触发异步清理GenServer.cast

💡 系统行为:
  - 房间进程终止时会广播:room_terminated事件
  - Registry.lookup失败时会触发{:cleanup_stale_room, room_id}
  - 清理是异步的，不会阻塞调用者
""")

Logger.info("🎉 [RUNTIME_TEST] 房间清理系统运行时测试完成!")
