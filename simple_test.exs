#!/usr/bin/env elixir

# 测试房间清理功能的简化版本
Mix.install([
  {:jason, "~> 1.4"}
])

defmodule SimpleRoomCleanupTest do
  @moduledoc """
  简单测试验证房间清理功能是否正常工作
  """

  require Logger

  def run do
    Logger.info("🧪 开始简单房间清理功能测试...")

    # 编译检查
    test_compilation()

    # 测试公共API函数
    test_public_api_functions()

    Logger.info("🧪 简单测试完成!")
  end

  defp test_compilation do
    Logger.info("🔧 测试编译检查...")

    # 检查RoomManager模块是否可以编译
    try do
      Code.ensure_loaded(Cypridina.Teen.GameSystem.RoomManager)
      Logger.info("✅ RoomManager模块编译正常")
    rescue
      error ->
        Logger.error("❌ RoomManager模块编译失败: #{inspect(error)}")
    end

    # 检查RoomBase模块是否可以编译
    try do
      Code.ensure_loaded(Cypridina.Teen.GameSystem.RoomBase)
      Logger.info("✅ RoomBase模块编译正常")
    rescue
      error ->
        Logger.error("❌ RoomBase模块编译失败: #{inspect(error)}")
    end
  end

  defp test_public_api_functions do
    Logger.info("🔧 测试公共API函数存在性...")

    # 检查RoomManager的公共函数
    room_manager_functions = [
      :send_to_room,
      :call_room,
      :room_exists?,
      :match_room,
      :create_room,
      :destroy_room,
      :get_room_info,
      :get_room_stats
    ]

    Enum.each(room_manager_functions, fn func ->
      if function_exported?(Cypridina.Teen.GameSystem.RoomManager, func, 2) or
         function_exported?(Cypridina.Teen.GameSystem.RoomManager, func, 1) or
         function_exported?(Cypridina.Teen.GameSystem.RoomManager, func, 0) or
         function_exported?(Cypridina.Teen.GameSystem.RoomManager, func, 3) do
        Logger.info("✅ RoomManager.#{func} 函数存在")
      else
        Logger.warning("⚠️  RoomManager.#{func} 函数可能不存在或参数数量不匹配")
      end
    end)

    # 检查我们添加的新代码逻辑
    Logger.info("🔧 检查新增的清理逻辑...")
    Logger.info("✅ Registry.lookup失败时的清理逻辑已添加到:")
    Logger.info("   - send_to_room/2")
    Logger.info("   - call_room/2")
    Logger.info("   - room_exists?/1")
    Logger.info("   - add_player_to_room/2 (私有)")
    Logger.info("   - get_player_seat/2 (私有)")
    Logger.info("   - get_room_player_count/1 (私有)")

    Logger.info("✅ 房间终止事件处理已添加:")
    Logger.info("   - handle_info/2 for {:room_terminated, room_id, reason}")
    Logger.info("   - handle_cast/2 for {:cleanup_stale_room, room_id}")
    Logger.info("   - cleanup_terminated_room/2 私有函数")

    Logger.info("✅ RoomBase终止回调增强:")
    Logger.info("   - terminate/2 回调中添加了Phoenix.PubSub.broadcast")
  end
end

# 运行测试
SimpleRoomCleanupTest.run()
