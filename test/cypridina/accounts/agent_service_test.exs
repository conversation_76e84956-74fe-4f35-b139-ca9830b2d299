defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.AgentServiceTest do
  use Cy<PERSON><PERSON>ina.DataCase

  alias <PERSON><PERSON><PERSON>ina.Accounts.{User, AgentService, AdminService}

  describe "代理级别检查" do
    test "is_agent?/1 正确识别代理用户" do
      # 创建不同级别的用户
      {:ok, non_agent} = create_user(%{agent_level: -1})
      {:ok, root_agent} = create_user(%{agent_level: 0})
      {:ok, level1_agent} = create_user(%{agent_level: 1})

      # 测试代理识别
      refute AgentService.is_agent?(non_agent)
      assert AgentService.is_agent?(root_agent)
      assert AgentService.is_agent?(level1_agent)
    end

    test "is_root_agent?/1 正确识别根代理" do
      {:ok, non_agent} = create_user(%{agent_level: -1})
      {:ok, root_agent} = create_user(%{agent_level: 0})
      {:ok, level1_agent} = create_user(%{agent_level: 1})

      refute AgentService.is_root_agent?(non_agent)
      assert AgentService.is_root_agent?(root_agent)
      refute AgentService.is_root_agent?(level1_agent)
    end

    test "has_agent_level?/2 正确检查代理级别" do
      {:ok, non_agent} = create_user(%{agent_level: -1})
      {:ok, root_agent} = create_user(%{agent_level: 0})
      {:ok, level2_agent} = create_user(%{agent_level: 2})

      # 非代理用户没有任何代理级别
      refute AgentService.has_agent_level?(non_agent, 0)
      refute AgentService.has_agent_level?(non_agent, 2)

      # 根代理具有所有级别的权限
      assert AgentService.has_agent_level?(root_agent, 0)
      assert AgentService.has_agent_level?(root_agent, 1)
      assert AgentService.has_agent_level?(root_agent, 2)

      # 2级代理只能管理2级及以下
      refute AgentService.has_agent_level?(level2_agent, 0)
      refute AgentService.has_agent_level?(level2_agent, 1)
      assert AgentService.has_agent_level?(level2_agent, 2)
      assert AgentService.has_agent_level?(level2_agent, 3)
    end

    test "get_agent_level/1 返回正确的代理级别" do
      {:ok, non_agent} = create_user(%{agent_level: -1})
      {:ok, root_agent} = create_user(%{agent_level: 0})
      {:ok, level3_agent} = create_user(%{agent_level: 3})

      assert AgentService.get_agent_level(non_agent) == -1
      assert AgentService.get_agent_level(root_agent) == 0
      assert AgentService.get_agent_level(level3_agent) == 3
    end

    test "root_agent_exists?/0 正确检查根代理是否存在" do
      # 初始状态没有根代理
      refute AgentService.root_agent_exists?()

      # 创建根代理
      {:ok, _root_agent} = create_user(%{agent_level: 0})
      assert AgentService.root_agent_exists?()

      # 创建其他级别的代理不影响结果
      {:ok, _level1_agent} = create_user(%{agent_level: 1})
      assert AgentService.root_agent_exists?()
    end
  end

  describe "后台访问权限" do
    test "can_access_admin?/1 正确检查后台访问权限" do
      {:ok, normal_user} = create_user(%{agent_level: -1, permission_level: 0})
      {:ok, agent_user} = create_user(%{agent_level: 0, permission_level: 0})
      {:ok, admin_user} = create_user(%{agent_level: -1, permission_level: 1})
      {:ok, agent_admin} = create_user(%{agent_level: 0, permission_level: 1})

      # 普通用户不能访问
      refute AgentService.can_access_admin?(normal_user)

      # 代理可以访问
      assert AgentService.can_access_admin?(agent_user)

      # 管理员可以访问
      assert AgentService.can_access_admin?(admin_user)

      # 代理管理员可以访问
      assert AgentService.can_access_admin?(agent_admin)
    end
  end

  # 辅助函数
  defp create_user(attrs \\ %{}) do
    default_attrs = %{
      username: "test_user_#{System.unique_integer([:positive])}",
      password: "password123",
      password_confirmation: "password123",
      agent_level: -1,
      permission_level: 0
    }

    attrs = Map.merge(default_attrs, attrs)

    User
    |> Ash.Changeset.for_create(:register_with_username, attrs)
    |> Ash.create()
  end
end
