defmodule Cypridina.ActivationWorkerTest do
  # 移除异步测试标志，因为 Sandbox 共享模式下不应使用异步测试
  use Cypridina.Case, async: false

  alias Ecto.Adapters.SQL.Sandbox

  setup do
    # 显式使用共享模式
    Sandbox.mode(Cypridina.Repo, {:shared, self()})
    :ok
  end

  test "activating a new user" do
    # user = MyApp.User.create(email: "<EMAIL>")

    :ok = Oban.Testing.perform_job(Cypridina.RacingGameScheduler, %{}, [])
  end
end
