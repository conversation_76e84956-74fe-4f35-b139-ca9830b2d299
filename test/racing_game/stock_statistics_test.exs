defmodule Cypridina.RacingGame.UserStockStatisticsTest do
  use ExUnit.Case, async: false
  alias <PERSON><PERSON><PERSON><PERSON>.RacingGame.UserStockStatistics

  setup do
    # 清理测试数据
    UserStockStatistics
    |> Ash.Query.new()
    |> Ash.bulk_destroy!(:destroy, %{}, return_errors?: true)

    :ok
  end

  describe "stock statistics" do
    test "creates stock statistics with valid data" do
      attrs = %{
        user_id: "test_user_123",
        total_points: 1000,
        total_wins: 5,
        total_losses: 3,
        win_rate: 62.5,
        biggest_win: 500,
        biggest_loss: 200,
        average_bet: 125.0,
        total_races: 8,
        profit_loss: 300
      }

      assert {:ok, stats} = UserStockStatistics.create(attrs)
      assert stats.user_id == "test_user_123"
      assert stats.total_points == 1000
      assert stats.total_wins == 5
      assert stats.total_losses == 3
      assert Decimal.equal?(stats.win_rate, Decimal.new("62.5"))
      assert stats.biggest_win == 500
      assert stats.biggest_loss == 200
      assert Decimal.equal?(stats.average_bet, Decimal.new("125.0"))
      assert stats.total_races == 8
      assert stats.profit_loss == 300
    end

    test "updates existing stock statistics" do
      attrs = %{
        user_id: "test_user_456",
        total_points: 500,
        total_wins: 2,
        total_losses: 1,
        win_rate: 66.7,
        biggest_win: 300,
        biggest_loss: 100,
        average_bet: 100.0,
        total_races: 3,
        profit_loss: 200
      }

      {:ok, stats} = UserStockStatistics.create(attrs)

      update_attrs = %{
        total_points: 1500,
        total_wins: 7,
        total_losses: 3,
        win_rate: 70.0,
        biggest_win: 600,
        biggest_loss: 150,
        average_bet: 150.0,
        total_races: 10,
        profit_loss: 900
      }

      assert {:ok, updated_stats} = UserStockStatistics.update(stats, update_attrs)
      assert updated_stats.total_points == 1500
      assert updated_stats.total_wins == 7
      assert updated_stats.total_losses == 3
      assert Decimal.equal?(updated_stats.win_rate, Decimal.new("70.0"))
      assert updated_stats.biggest_win == 600
      assert updated_stats.biggest_loss == 150
      assert Decimal.equal?(updated_stats.average_bet, Decimal.new("150.0"))
      assert updated_stats.total_races == 10
      assert updated_stats.profit_loss == 900
    end

    test "gets stock statistics by user_id" do
      attrs = %{
        user_id: "test_user_789",
        total_points: 2000,
        total_wins: 10,
        total_losses: 5,
        win_rate: 66.7,
        biggest_win: 800,
        biggest_loss: 300,
        average_bet: 200.0,
        total_races: 15,
        profit_loss: 1000
      }

      {:ok, _stats} = UserStockStatistics.create(attrs)

      assert {:ok, found_stats} = UserStockStatistics.get_by_user_id(%{user_id: "test_user_789"})
      assert found_stats.user_id == "test_user_789"
      assert found_stats.total_points == 2000
      assert found_stats.total_wins == 10
    end

    test "returns error when user not found" do
      assert {:error, %Ash.Error.Invalid{}} = UserStockStatistics.get_by_user_id(%{user_id: "nonexistent_user"})
    end

    test "lists all stock statistics" do
      # Create multiple statistics
      attrs1 = %{
        user_id: "user_1",
        total_points: 1000,
        total_wins: 5,
        total_losses: 3,
        win_rate: 62.5,
        biggest_win: 500,
        biggest_loss: 200,
        average_bet: 125.0,
        total_races: 8,
        profit_loss: 300
      }

      attrs2 = %{
        user_id: "user_2",
        total_points: 1500,
        total_wins: 8,
        total_losses: 2,
        win_rate: 80.0,
        biggest_win: 600,
        biggest_loss: 150,
        average_bet: 150.0,
        total_races: 10,
        profit_loss: 900
      }

      {:ok, _stats1} = UserStockStatistics.create(attrs1)
      {:ok, _stats2} = UserStockStatistics.create(attrs2)

      {:ok, all_stats} = UserStockStatistics.list()
      assert length(all_stats) >= 2

      user_ids = Enum.map(all_stats, & &1.user_id)
      assert "user_1" in user_ids
      assert "user_2" in user_ids
    end

    test "validates required fields" do
      attrs = %{}

      assert {:error, %Ash.Error.Invalid{}} = UserStockStatistics.create(attrs)
    end

    test "validates numeric fields are non-negative" do
      attrs = %{
        user_id: "test_user",
        total_points: -100,  # Invalid negative value
        total_wins: 5,
        total_losses: 3,
        win_rate: 62.5,
        biggest_win: 500,
        biggest_loss: 200,
        average_bet: 125.0,
        total_races: 8,
        profit_loss: 300
      }

      assert {:error, %Ash.Error.Invalid{}} = UserStockStatistics.create(attrs)
    end
  end
end
