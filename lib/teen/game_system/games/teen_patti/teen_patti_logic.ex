defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiLogic do
  @moduledoc """
  Teen Patti游戏逻辑模块

  负责处理Teen Patti游戏的核心逻辑：
  - 牌堆管理和洗牌
  - 发牌逻辑
  - 牌型评估和比较
  - 游戏规则验证
  """

  require Logger

  # 牌型等级 (从小到大)
  @hand_ranks %{
    # 高牌
    high_card: 1,
    # 对子
    pair: 2,
    # 同花
    flush: 3,
    # 顺子
    straight: 4,
    # 同花顺
    straight_flush: 5,
    # 三条
    three_of_kind: 6
  }

  # 花色
  @suits [:hearts, :diamonds, :clubs, :spades]

  # 牌值 (A=1, J=11, Q=12, K=13)
  @ranks [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]

  @doc """
  创建一副标准52张牌的牌堆并洗牌
  """
  def create_deck do
    deck =
      for suit <- @suits, rank <- @ranks do
        %{suit: suit, rank: rank}
      end

    Enum.shuffle(deck)
  end

  @doc """
  给指定玩家发牌，每人3张
  """
  def deal_cards_to_players(deck, players, active_player_ids) do
    {remaining_deck, updated_players} =
      Enum.reduce(active_player_ids, {deck, players}, fn user_id,
                                                         {current_deck, current_players} ->
        {cards, new_deck} = draw_cards(current_deck, 3)
        updated_player = %{current_players[user_id] | cards: cards}
        new_players = Map.put(current_players, user_id, updated_player)
        {new_deck, new_players}
      end)

    {remaining_deck, updated_players}
  end

  @doc """
  从牌堆中抽取指定数量的牌
  """
  def draw_cards(deck, count) do
    {cards, remaining} = Enum.split(deck, count)
    {cards, remaining}
  end

  @doc """
  评估手牌的牌型和强度
  返回: %{rank: 牌型等级, value: 比较值, description: 描述}
  """
  def evaluate_hand(cards) when length(cards) == 3 do
    sorted_cards = Enum.sort_by(cards, & &1.rank, :desc)

    cond do
      is_three_of_kind?(sorted_cards) ->
        %{
          rank: @hand_ranks.three_of_kind,
          value: get_three_of_kind_value(sorted_cards),
          description: "三条"
        }

      is_straight_flush?(sorted_cards) ->
        %{
          rank: @hand_ranks.straight_flush,
          value: get_straight_value(sorted_cards),
          description: "同花顺"
        }

      is_straight?(sorted_cards) ->
        %{rank: @hand_ranks.straight, value: get_straight_value(sorted_cards), description: "顺子"}

      is_flush?(sorted_cards) ->
        %{rank: @hand_ranks.flush, value: get_flush_value(sorted_cards), description: "同花"}

      is_pair?(sorted_cards) ->
        %{rank: @hand_ranks.pair, value: get_pair_value(sorted_cards), description: "对子"}

      true ->
        %{
          rank: @hand_ranks.high_card,
          value: get_high_card_value(sorted_cards),
          description: "高牌"
        }
    end
  end

  def evaluate_hand(_cards), do: %{rank: 0, value: 0, description: "无效手牌"}

  @doc """
  比较两手牌的大小
  返回: :greater | :equal | :less
  """
  def compare_hands(hand1, hand2) do
    eval1 = evaluate_hand(hand1)
    eval2 = evaluate_hand(hand2)

    cond do
      eval1.rank > eval2.rank -> :greater
      eval1.rank < eval2.rank -> :less
      eval1.value > eval2.value -> :greater
      eval1.value < eval2.value -> :less
      true -> :equal
    end
  end

  @doc """
  验证下注金额是否有效
  """
  def validate_bet(amount, min_bet, max_bet, player_chips) do
    cond do
      amount < min_bet -> {:error, "下注金额低于最小值"}
      amount > max_bet -> {:error, "下注金额超过最大值"}
      amount > player_chips -> {:error, "筹码不足"}
      true -> :ok
    end
  end

  @doc """
  计算摊牌费用
  """
  def calculate_show_cost(current_bet, multiplier \\ 2) do
    current_bet * multiplier
  end

  @doc """
  获取牌的显示名称
  """
  def card_display_name(%{suit: suit, rank: rank}) do
    suit_name =
      case suit do
        :hearts -> "♥"
        :diamonds -> "♦"
        :clubs -> "♣"
        :spades -> "♠"
      end

    rank_name =
      case rank do
        1 -> "A"
        11 -> "J"
        12 -> "Q"
        13 -> "K"
        n -> to_string(n)
      end

    "#{rank_name}#{suit_name}"
  end

  @doc """
  格式化手牌为显示字符串
  """
  def format_hand(cards) do
    cards
    |> Enum.map(&card_display_name/1)
    |> Enum.join(" ")
  end

  # 私有函数

  defp is_three_of_kind?(cards) do
    ranks = Enum.map(cards, & &1.rank)
    length(Enum.uniq(ranks)) == 1
  end

  defp is_straight_flush?(cards) do
    is_flush?(cards) and is_straight?(cards)
  end

  defp is_straight?(cards) do
    ranks = Enum.map(cards, & &1.rank) |> Enum.sort()

    # 检查连续性
    case ranks do
      # A-Q-K 特殊顺子
      [1, 12, 13] -> true
      [a, b, c] when b == a + 1 and c == b + 1 -> true
      _ -> false
    end
  end

  defp is_flush?(cards) do
    suits = Enum.map(cards, & &1.suit)
    length(Enum.uniq(suits)) == 1
  end

  defp is_pair?(cards) do
    ranks = Enum.map(cards, & &1.rank)
    length(Enum.uniq(ranks)) == 2
  end

  defp get_three_of_kind_value(cards) do
    # 三条的值就是牌的点数
    hd(cards).rank * 1000
  end

  defp get_straight_value(cards) do
    ranks = Enum.map(cards, & &1.rank) |> Enum.sort()

    case ranks do
      # A-Q-K 作为最大的顺子
      [1, 12, 13] -> 14 * 100
      # 以最小牌为基准
      [a, _, _] -> a * 100
    end
  end

  defp get_flush_value(cards) do
    # 同花按最大牌计算，然后是第二大，第三大
    sorted_ranks = Enum.map(cards, & &1.rank) |> Enum.sort(:desc)

    Enum.with_index(sorted_ranks)
    |> Enum.reduce(0, fn {rank, index}, acc ->
      acc + rank * :math.pow(100, 2 - index)
    end)
    |> trunc()
  end

  defp get_pair_value(cards) do
    ranks = Enum.map(cards, & &1.rank)
    rank_counts = Enum.frequencies(ranks)

    {pair_rank, _} = Enum.find(rank_counts, fn {_rank, count} -> count == 2 end)
    {kicker, _} = Enum.find(rank_counts, fn {_rank, count} -> count == 1 end)

    pair_rank * 100 + kicker
  end

  defp get_high_card_value(cards) do
    # 高牌按最大牌计算，然后是第二大，第三大
    sorted_ranks = Enum.map(cards, & &1.rank) |> Enum.sort(:desc)

    Enum.with_index(sorted_ranks)
    |> Enum.reduce(0, fn {rank, index}, acc ->
      acc + rank * :math.pow(10, 2 - index)
    end)
    |> trunc()
  end

  @doc """
  生成机器人决策
  基于手牌强度和当前游戏状态做出决策
  """
  def robot_decision(cards, current_bet, player_bet, player_chips, pot_odds \\ 0.3) do
    hand_eval = evaluate_hand(cards)
    hand_strength = calculate_hand_strength(hand_eval)

    call_amount = current_bet - player_bet

    cond do
      # 强牌 - 加注
      hand_strength >= 0.8 ->
        raise_amount = min(current_bet, div(player_chips, 4))
        {:raise, raise_amount}

      # 中等牌 - 根据赔率决定
      hand_strength >= 0.5 ->
        if call_amount <= player_chips * pot_odds do
          {:call, call_amount}
        else
          {:fold, 0}
        end

      # 弱牌但便宜 - 跟注
      hand_strength >= 0.3 and call_amount <= player_chips * 0.1 ->
        {:call, call_amount}

      # 弱牌 - 弃牌
      true ->
        {:fold, 0}
    end
  end

  defp calculate_hand_strength(%{rank: rank, value: _value}) do
    case rank do
      # 三条
      6 -> 1.0
      # 同花顺
      5 -> 0.9
      # 顺子
      4 -> 0.8
      # 同花
      3 -> 0.7
      # 对子
      2 -> 0.5
      # 高牌
      1 -> 0.2
      _ -> 0.0
    end
  end

  @doc """
  检查游戏是否应该结束
  """
  def should_game_end?(active_players, max_rounds, current_round) do
    length(active_players) <= 1 or current_round >= max_rounds
  end

  @doc """
  计算边池 (side pot) - 当玩家全押时使用
  """
  def calculate_side_pots(players) do
    # 简化版本，暂时不实现边池逻辑
    # 在实际游戏中，当玩家全押金额不同时需要计算边池
    []
  end

  @doc """
  验证玩家动作是否合法
  """
  def validate_action(action, current_bet, player_bet, player_chips) do
    case action do
      {:fold, _} ->
        :ok

      {:call, amount} ->
        required = current_bet - player_bet

        if amount == required and player_chips >= required do
          :ok
        else
          {:error, "跟注金额不正确"}
        end

      {:raise, amount} ->
        total_required = current_bet - player_bet + amount

        if player_chips >= total_required and amount > 0 do
          :ok
        else
          {:error, "加注金额无效"}
        end

      {:check, _} ->
        if current_bet == player_bet do
          :ok
        else
          {:error, "有下注时不能过牌"}
        end

      _ ->
        {:error, "无效动作"}
    end
  end

  @doc """
  生成游戏统计信息
  """
  def generate_game_stats(players, winner, pot) do
    %{
      total_players: map_size(players),
      winner: winner,
      pot_size: pot,
      hands_shown: get_shown_hands(players),
      game_duration: calculate_game_duration(players)
    }
  end

  defp get_shown_hands(players) do
    players
    |> Enum.filter(fn {_id, player} -> player.status != :folded end)
    |> Enum.map(fn {id, player} ->
      {id, %{cards: player.cards, evaluation: evaluate_hand(player.cards)}}
    end)
    |> Enum.into(%{})
  end

  defp calculate_game_duration(players) do
    if map_size(players) > 0 do
      start_times = players |> Map.values() |> Enum.map(& &1.joined_at)
      current_time = System.system_time(:millisecond)
      current_time - Enum.min(start_times)
    else
      0
    end
  end
end
