defmodule Cypridina.Teen.GameSystem.Games.LongHu.LongHuLogic do
  @moduledoc """
  龙虎斗游戏逻辑模块

  游戏规则：
  - 使用标准52张扑克牌（不含大小王）
  - 每局发两张牌：龙牌和虎牌
  - 比较牌面大小，A最小(1)，K最大(13)
  - 龙大于虎 -> 龙赢
  - 虎大于龙 -> 虎赢
  - 龙等于虎 -> 和
  """

  require Logger

  # 扑克牌定义
  # 黑桃、红心、方块、梅花
  @suits [:spades, :hearts, :diamonds, :clubs]
  # A, 2-10, J, Q, K
  @ranks [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]

  # 游戏结果
  @results %{
    # 龙赢
    long: :long,
    # 虎赢
    hu: :hu,
    # 和
    he: :he
  }

  @doc """
  创建一副标准扑克牌

  ## 返回
  包含52张牌的列表，每张牌格式为 %{suit: :spades, rank: 1, value: 1}
  """
  def create_deck() do
    for suit <- @suits, rank <- @ranks do
      %{
        suit: suit,
        rank: rank,
        # 牌面值，用于比较大小
        value: rank
      }
    end
  end

  @doc """
  洗牌

  ## 参数
  - deck: 牌组

  ## 返回
  洗牌后的牌组
  """
  def shuffle_deck(deck) do
    Enum.shuffle(deck)
  end

  @doc """
  发牌 - 发出龙牌和虎牌

  ## 返回
  {龙牌, 虎牌} 元组
  """
  def deal_cards() do
    deck = create_deck() |> shuffle_deck()

    [long_card, hu_card | _rest] = deck

    Logger.info("🐉 [LONGHU_LOGIC] 发牌: 龙=#{format_card(long_card)}, 虎=#{format_card(hu_card)}")

    {long_card, hu_card}
  end

  @doc """
  计算游戏结果

  ## 参数
  - long_card: 龙牌
  - hu_card: 虎牌

  ## 返回
  :long | :hu | :he
  """
  def calculate_result(long_card, hu_card) do
    long_value = long_card.value
    hu_value = hu_card.value

    result =
      cond do
        long_value > hu_value -> @results.long
        hu_value > long_value -> @results.hu
        long_value == hu_value -> @results.he
      end

    Logger.info("🐉 [LONGHU_LOGIC] 游戏结果: 龙=#{long_value}, 虎=#{hu_value} -> #{result}")

    result
  end

  @doc """
  计算下注赔付

  ## 参数
  - bet_area: 下注区域 (:long, :hu, :he)
  - bet_amount: 下注金额
  - result: 游戏结果
  - odds: 赔率配置

  ## 返回
  赔付金额（正数表示赢钱，负数表示输钱）
  """
  def calculate_payout(bet_area, bet_amount, result, odds) do
    if bet_area == result do
      # 赢了，获得本金 + 奖金
      bet_amount + bet_amount * (Map.get(odds, bet_area, 1.0) - 1.0)
    else
      # 输了，失去本金
      -bet_amount
    end
  end

  @doc """
  验证下注是否有效

  ## 参数
  - area: 下注区域
  - amount: 下注金额
  - config: 游戏配置

  ## 返回
  {:ok, :valid} | {:error, reason}
  """
  def validate_bet(area, amount, config) do
    cond do
      area not in [:long, :hu, :he] ->
        {:error, "无效的下注区域"}

      not is_number(amount) or amount <= 0 ->
        {:error, "下注金额必须大于0"}

      amount < config.min_bet ->
        {:error, "下注金额低于最小限制"}

      amount > config.max_bet ->
        {:error, "下注金额超过最大限制"}

      true ->
        {:ok, :valid}
    end
  end

  @doc """
  获取历史统计信息

  ## 参数
  - history: 历史记录列表
  - count: 统计最近多少局（默认20局）

  ## 返回
  统计信息 Map
  """
  def get_statistics(history, count \\ 20) do
    recent_history = Enum.take(history, count)

    total_games = length(recent_history)

    if total_games == 0 do
      %{
        total_games: 0,
        long_wins: 0,
        hu_wins: 0,
        he_wins: 0,
        long_rate: 0.0,
        hu_rate: 0.0,
        he_rate: 0.0
      }
    else
      results = Enum.map(recent_history, & &1.result)

      long_wins = Enum.count(results, &(&1 == :long))
      hu_wins = Enum.count(results, &(&1 == :hu))
      he_wins = Enum.count(results, &(&1 == :he))

      %{
        total_games: total_games,
        long_wins: long_wins,
        hu_wins: hu_wins,
        he_wins: he_wins,
        long_rate: Float.round(long_wins / total_games * 100, 1),
        hu_rate: Float.round(hu_wins / total_games * 100, 1),
        he_rate: Float.round(he_wins / total_games * 100, 1)
      }
    end
  end

  @doc """
  获取热门下注区域

  ## 参数
  - total_bets: 当前局的总下注 %{long: amount, hu: amount, he: amount}

  ## 返回
  下注最多的区域
  """
  def get_popular_bet_area(total_bets) do
    total_bets
    |> Enum.max_by(fn {_area, amount} -> amount end)
    |> elem(0)
  end

  @doc """
  生成机器人下注策略

  ## 参数
  - history: 历史记录
  - total_bets: 当前总下注
  - config: 游戏配置

  ## 返回
  {下注区域, 下注金额}
  """
  def generate_robot_bet(history, total_bets, config) do
    # 简单的机器人策略：
    # 1. 70% 概率跟随热门区域
    # 2. 20% 概率随机选择
    # 3. 10% 概率反向下注（选择冷门区域）

    strategy = :rand.uniform(100)

    area =
      cond do
        strategy <= 70 ->
          # 跟随热门
          get_popular_bet_area(total_bets)

        strategy <= 90 ->
          # 随机选择
          Enum.random([:long, :hu, :he])

        true ->
          # 反向下注
          popular = get_popular_bet_area(total_bets)
          [:long, :hu, :he] |> Enum.reject(&(&1 == popular)) |> Enum.random()
      end

    # 随机下注金额（在配置范围内）
    min_bet = config.min_bet
    # 限制机器人最大下注
    max_bet = min(config.max_bet, min_bet * 10)

    amount = min_bet + :rand.uniform(max_bet - min_bet)

    {area, amount}
  end

  @doc """
  格式化扑克牌显示

  ## 参数
  - card: 扑克牌 %{suit: :spades, rank: 1, value: 1}

  ## 返回
  格式化的字符串，如 "♠A", "♥K"
  """
  def format_card(%{suit: suit, rank: rank}) do
    suit_symbol =
      case suit do
        :spades -> "♠"
        :hearts -> "♥"
        :diamonds -> "♦"
        :clubs -> "♣"
      end

    rank_symbol =
      case rank do
        1 -> "A"
        11 -> "J"
        12 -> "Q"
        13 -> "K"
        n -> to_string(n)
      end

    "#{suit_symbol}#{rank_symbol}"
  end

  @doc """
  将扑克牌转换为客户端格式

  ## 参数
  - card: 扑克牌

  ## 返回
  客户端可识别的牌面数据
  """
  def card_to_client_format(card) do
    %{
      suit: card.suit,
      rank: card.rank,
      value: card.value,
      display: format_card(card)
    }
  end

  @doc """
  检查游戏是否可以开始

  ## 参数
  - player_count: 当前玩家数量
  - min_players: 最小玩家数量

  ## 返回
  boolean
  """
  def can_start_game?(player_count, min_players) do
    player_count >= min_players
  end

  @doc """
  获取下一个游戏阶段

  ## 参数
  - current_phase: 当前阶段

  ## 返回
  下一个阶段
  """
  def next_phase(current_phase) do
    case current_phase do
      :waiting -> :betting
      :betting -> :dealing
      :dealing -> :revealing
      :revealing -> :settling
      :settling -> :waiting
      _ -> :waiting
    end
  end
end
