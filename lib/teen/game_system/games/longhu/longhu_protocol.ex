defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.LongHu.LongHuProtocol do
  @moduledoc """
  龙虎斗游戏协议处理模块

  负责处理龙虎斗游戏相关的网络协议，包括：
  - 加入/离开房间
  - 下注操作
  - 游戏状态同步
  - 结果广播
  """

  require Logger

  alias Cypridina.Teen.GameSystem.{RoomManager, Room}
  alias Cypridina.Teen.GameSystem.Games.LongHu.{LongHuRoom, LongHuLogic}

  # 龙虎斗协议ID定义 (基于IndiaGameServer的协议)
  @protocol_ids %{
    # 客户端请求
    # 加入房间
    join_room: {201, 1},
    # 离开房间
    leave_room: {201, 2},
    # 下注
    place_bet: {201, 3},
    # 获取房间信息
    get_room_info: {201, 4},
    # 获取历史记录
    get_history: {201, 5},
    # 申请上庄
    apply_banker: {201, 6},
    # 取消申请上庄
    cancel_apply_banker: {201, 7},
    # 申请下庄
    apply_off_banker: {201, 8},
    # 请求上庄列表
    request_banker_list: {201, 9},
    # 请求玩家列表
    request_player_list: {201, 10},
    # 续押
    follow_bet: {201, 11},
    # 请求奖池信息
    request_jackpot: {201, 12},
    # 请求状态时间配置
    request_time_config: {201, 13},
    # 请求游戏统计
    request_statistics: {201, 14},
    # 请求个人战绩
    request_personal_stats: {201, 15},
    # 发送聊天消息
    send_chat_message: {201, 16},
    # 请求游戏录像
    request_game_replay: {201, 17},

    # 服务器响应
    # 加入房间结果
    join_room_result: {201, 21},
    # 离开房间结果
    leave_room_result: {201, 22},
    # 下注结果
    bet_result: {201, 23},
    # 房间信息
    room_info: {201, 24},
    # 历史数据
    history_data: {201, 25},
    # 申请上庄结果
    apply_banker_result: {201, 26},
    # 取消申请上庄结果
    cancel_apply_banker_result: {201, 27},
    # 申请下庄结果
    apply_off_banker_result: {201, 28},
    # 上庄列表
    banker_list: {201, 29},
    # 玩家列表
    player_list: {201, 30},
    # 续押结果
    follow_bet_result: {201, 31},
    # 奖池信息
    jackpot_info: {201, 32},
    # 时间配置
    time_config: {201, 33},
    # 游戏统计
    game_statistics: {201, 34},
    # 个人战绩
    personal_stats: {201, 35},
    # 聊天消息广播
    chat_message: {201, 36},
    # 游戏录像
    game_replay: {201, 37},

    # 游戏状态广播
    # 游戏开始
    game_start: {201, 41},
    # 下注开始
    betting_start: {201, 42},
    # 下注结束
    betting_end: {201, 43},
    # 发牌
    dealing: {201, 44},
    # 亮牌
    revealing: {201, 45},
    # 结算
    settlement: {201, 46},
    # 玩家加入
    player_joined: {201, 47},
    # 玩家离开
    player_left: {201, 48},
    # 下注更新
    bet_update: {201, 49},
    # 庄家信息更新
    banker_info: {201, 50},
    # 下庄通知
    banker_off: {201, 51}
  }

  # 客户端协议ID映射 (对应IndiaGameClient中的LHDefine.ts)
  @client_protocol_ids %{
    # 对应客户端 SC_LHD_CONFIG_P (3900) - 发送配置
    sc_lhd_config: 3900,
    # 对应客户端 SC_LHD_GAMESTATE_P (3901) - 游戏状态切换
    sc_lhd_gamestate: 3901,
    # 对应客户端 SC_LHD_FIRST_P (3902) - 开始发牌动画
    sc_lhd_first: 3902,
    # 对应客户端 SC_LHD_OPTTIME_P (3903) - 下注亮牌时间
    sc_lhd_opttime: 3903,
    # 对应客户端 CS_LHD_BUYHORSE_P (3904) - 请求下注
    cs_lhd_buyhorse: 3904,
    # 对应客户端 SC_LHD_BUYHORSE_P (3905) - 下注响应
    sc_lhd_buyhorse: 3905,
    # 对应客户端 CS_LHD_REQUEST_ZHUANG_P (3906) - 请求上庄
    cs_lhd_request_zhuang: 3906,
    # 对应客户端 CS_LHD_REQUEST_NOT_ZHUANG_P (3907) - 请求取消上庄
    cs_lhd_request_not_zhuang: 3907,
    # 对应客户端 SC_LHD_ZHUANG_LIST_P (3908) - 上庄列表
    sc_lhd_zhuang_list: 3908,
    # 对应客户端 SC_LHD_ZHUANG_INFO_P (3909) - 庄家信息
    sc_lhd_zhuang_info: 3909,
    # 对应客户端 SC_LHD_NO_ZHUANG_P (3910) - 下庄公告
    sc_lhd_no_zhuang: 3910,
    # 对应客户端 SC_LHD_NOTICE_NO_ZHUANG_P (3911) - 通知庄家可以开始主动下庄
    sc_lhd_notice_no_zhuang: 3911,
    # 对应客户端 SC_LHD_SHOWCARD_P (3912) - 通知亮牌操作
    sc_lhd_showcard: 3912,
    # 对应客户端 SC_LHD_SETTLEMENT_P (3913) - 比牌结果&结算
    sc_lhd_settlement: 3913,
    # 对应客户端 SC_LHD_OPER_ERROR_P (3914) - 服务端返回操作错误码
    sc_lhd_oper_error: 3914,
    # 对应客户端 CS_LHD_HISTORY_P (3915) - 请求历史信息
    cs_lhd_history: 3915,
    # 对应客户端 SC_LHD_HISTORY_P (3916) - 返回历史信息
    sc_lhd_history: 3916,
    # 对应客户端 CS_LHD_FOLLOW_BUY_P (3917) - 请求续投
    cs_lhd_follow_buy: 3917,
    # 对应客户端 SC_LHD_FOLLOW_BUY_P (3918) - 续投
    sc_lhd_follow_buy: 3918,
    # 对应客户端 CS_LHD_ZHUANG_OFF_P (3919) - 当前庄请求下庄
    cs_lhd_zhuang_off: 3919,
    # 对应客户端 CS_LHD_ALLLIST_P (3920) - 请求玩家列表
    cs_lhd_alllist: 3920,
    # 对应客户端 SC_LHD_ALLLIST_P (3921) - 返回玩家列表
    sc_lhd_alllist: 3921,
    # 对应客户端 SC_LHD_BETINFO (3922) - 下注信息
    sc_lhd_betinfo: 3922,
    # 对应客户端 SC_LHD_SYNC_BET (3923) - 下注同步
    sc_lhd_sync_bet: 3923,
    # 对应客户端 CS_LHD_REQUEST_ZHUANG_LIST_P (3924) - 请求上庄列表
    cs_lhd_request_zhuang_list: 3924,
    # 对应客户端 SC_LHD_BET_SUCCESS (3926) - 下注成功
    sc_lhd_bet_success: 3926,
    # 对应客户端 SC_LHD_BET_SYNC (3927) - 筹码增量信息
    sc_lhd_bet_sync: 3927,
    # 对应客户端 SC_LHD_SETTLEMENT_P_NEW (3928) - 新结算协议
    sc_lhd_settlement_new: 3928,
    # 对应客户端 SC_LHD_ONLINENUM_P (3929) - 桌面上在线玩家数
    sc_lhd_onlinenum: 3929
  }

  @doc """
  处理加入龙虎斗房间请求

  ## 参数
  - message: 协议消息 %{main_id: 201, sub_id: 1, data: %{room_id: "xxx", user_info: %{}}}
  - state: 连接状态

  ## 返回
  {:reply, response, new_state}
  """
  def handle_join_room(%{main_id: 201, sub_id: 1} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理加入房间请求: #{inspect(message.data)}")

    room_id = Map.get(message.data, "room_id")
    user_info = Map.get(message.data, "user_info", %{})
    user_id = state.user_id

    cond do
      is_nil(room_id) ->
        # 创建新房间
        case create_longhu_room(user_id, user_info) do
          {:ok, new_room_id} ->
            new_state = Map.merge(state, %{current_room: new_room_id, game_type: :longhu})

            response =
              build_response(201, 11, %{
                "code" => 0,
                "msg" => "创建房间成功",
                "room_id" => new_room_id,
                "is_creator" => true
              })

            {:reply, response, new_state}

          {:error, reason} ->
            response =
              build_response(201, 11, %{
                "code" => 1,
                "msg" => "创建房间失败: #{reason}"
              })

            {:reply, response, state}
        end

      RoomManager.room_exists?(room_id) ->
        # 加入现有房间
        case LongHuRoom.join_room(room_id, user_id, user_info) do
          {:ok, :joined} ->
            new_state = Map.merge(state, %{current_room: room_id, game_type: :longhu})

            response =
              build_response(201, 11, %{
                "code" => 0,
                "msg" => "加入房间成功",
                "room_id" => room_id,
                "is_creator" => false
              })

            {:reply, response, new_state}

          {:ok, :rejoined} ->
            new_state = Map.merge(state, %{current_room: room_id, game_type: :longhu})

            response =
              build_response(201, 11, %{
                "code" => 0,
                "msg" => "重新加入房间成功",
                "room_id" => room_id,
                "is_creator" => false
              })

            {:reply, response, new_state}

          {:error, reason} ->
            response =
              build_response(201, 11, %{
                "code" => 1,
                "msg" => "加入房间失败: #{reason}"
              })

            {:reply, response, state}
        end

      true ->
        response =
          build_response(201, 11, %{
            "code" => 1,
            "msg" => "房间不存在"
          })

        {:reply, response, state}
    end
  end

  @doc """
  处理离开龙虎斗房间请求
  """
  def handle_leave_room(%{main_id: 201, sub_id: 2} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理离开房间请求: #{inspect(message.data)}")

    if state.current_room do
      case LongHuRoom.leave_room(state.current_room, state.user_id) do
        {:ok, :left} ->
          new_state = Map.merge(state, %{current_room: nil, game_type: nil})

          response =
            build_response(201, 12, %{
              "code" => 0,
              "msg" => "离开房间成功"
            })

          {:reply, response, new_state}

        {:error, reason} ->
          response =
            build_response(201, 12, %{
              "code" => 1,
              "msg" => "离开房间失败: #{reason}"
            })

          {:reply, response, state}
      end
    else
      response =
        build_response(201, 12, %{
          "code" => 1,
          "msg" => "未在任何房间中"
        })

      {:reply, response, state}
    end
  end

  @doc """
  处理下注请求 - 直接处理客户端原始消息
  """
  def handle_place_bet(%{main_id: 5, sub_id: 3904} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端下注请求: #{inspect(message.data)}")

    # 解析客户端数据格式
    data = message.data || %{}
    direction = Map.get(data, "direction", 1)  # 客户端发送的方向 (1=龙, 2=虎, 3=和)
    odds = Map.get(data, "odds", 0)            # 客户端发送的下注金额

    # 转换方向为内部格式 (客户端数据: 1=龙, 2=虎, 3=和)
    area = case direction do
      1 -> "long"  # 龙
      2 -> "hu"    # 虎
      3 -> "he"    # 和
      _ -> "long"  # 默认为龙
    end

    amount = odds

    # 验证下注参数
    cond do
      is_nil(area) or area == "" ->
        response = build_client_response(5, 3914, %{
          "errorcode" => 15  # GAME_ERROR_BUY_POS_ERROR
        })
        {:reply, response, state}

      is_nil(amount) or amount <= 0 ->
        response = build_client_response(5, 3914, %{
          "errorcode" => 8   # GAME_ERROR_NOT_MONEY_TO_BET
        })
        {:reply, response, state}

      # not (Map.has_key?(state, :current_room) and Map.get(state, :current_room) and Map.get(state, :game_type) == :longhu) ->
      #   # 如果用户没有加入房间，自动加入龙虎斗房间
      #   Logger.info("🐉 [AUTO_JOIN] 用户未在房间中，自动加入龙虎斗房间 - 用户: #{state.user_id}")

      #   # 自动加入房间逻辑
      #   user_info = %{
      #     nickname: Map.get(state, :nickname, "玩家#{state.user_id}"),
      #     avatar: Map.get(state, :avatar, "default"),
      #     money: Map.get(state, :money, 10000)
      #   }

      #   # 构建房间ID (使用游戏ID和服务器ID)
      #   game_id = Map.get(state, :game_id, 22)
      #   server_id = Map.get(state.channel_info || %{}, :server_id, 2201)
      #   room_id = "room_#{game_id}_#{server_id}"

      #   case auto_join_longhu_room(room_id, state.user_id, user_info, state) do
      #     {:ok, new_state} ->
      #       # 成功加入房间后，继续处理下注
      #       handle_bet_after_join_client(new_state, area, amount)

      #     {:error, reason} ->
      #       response = build_client_response(5, 3914, %{
      #         "errorcode" => 1  # 通用错误
      #       })
      #       {:reply, response, state}
      #   end

      true ->
        # 转换区域名称为原子
        area_atom = case area do
          "long" -> :long
          "hu" -> :hu
          "he" -> :he
          _ -> :long
        end

        # 使用内部消息格式，不需要协议ID (房间内部处理)
        bet_message = %{"cmd" => "bet", "area" => area_atom, "amount" => amount}

        case LongHuRoom.send_game_message(state.current_room, state.user_id, bet_message) do
          :ok ->
            # 下注成功，客户端会收到房间的广播消息，这里不需要额外响应
            {:ok, state}

          {:error, reason} ->
            response = build_client_response(5, 3914, %{
              "errorcode" => 1  # 通用错误
            })
            {:reply, response, state}
        end
    end
  end

  @doc """
  处理获取房间信息请求
  """
  def handle_get_room_info(%{main_id: 201, sub_id: 4} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理获取房间信息请求: #{inspect(message.data)}")

    if state.current_room do
      info_message = %{"cmd" => "get_room_info"}
      LongHuRoom.send_game_message(state.current_room, state.user_id, info_message)

      response =
        build_response(201, 14, %{
          "code" => 0,
          "msg" => "房间信息请求已发送"
        })

      {:reply, response, state}
    else
      response =
        build_response(201, 14, %{
          "code" => 1,
          "msg" => "未在任何房间中"
        })

      {:reply, response, state}
    end
  end

  @doc """
  处理获取历史记录请求
  """
  def handle_get_history(%{main_id: 201, sub_id: 5} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理获取历史记录请求: #{inspect(message.data)}")

    if state.current_room  do
      history_message = %{"cmd" => "get_history"}
      LongHuRoom.send_game_message(state.current_room, state.user_id, history_message)

      response =
        build_response(201, 15, %{
          "code" => 0,
          "msg" => "历史记录请求已发送"
        })

      {:reply, response, state}
    else
      response =
        build_response(201, 15, %{
          "code" => 1,
          "msg" => "未在龙虎斗房间中"
        })

      {:reply, response, state}
    end
  end

  @doc """
  处理申请上庄请求 - 直接处理客户端原始消息
  """
  def handle_apply_banker(%{main_id: 5, sub_id: 3906} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端申请上庄请求: #{inspect(message.data)}")

    if state.current_room  do
      banker_message = %{"cmd" => "apply_banker"}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, banker_message) do
        :ok ->
          # 申请成功，客户端会收到房间的广播消息，这里不需要额外响应
          {:ok, state}

        {:error, reason} ->
          response = build_client_response(5, 3914, %{
            "errorcode" => 7  # GAME_ERROR_APPLYZHUANG_OK (已申请)
          })
          {:reply, response, state}
      end
    else
      response = build_client_response(5, 3914, %{
        "errorcode" => 1  # 通用错误
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理取消申请上庄请求 - 直接处理客户端原始消息
  """
  def handle_cancel_apply_banker(%{main_id: 5, sub_id: 3907} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端取消申请上庄请求: #{inspect(message.data)}")

    if state.current_room  do
      banker_message = %{"cmd" => "cancel_apply_banker"}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, banker_message) do
        :ok ->
          # 取消成功，客户端会收到房间的广播消息，这里不需要额外响应
          {:ok, state}

        {:error, reason} ->
          response = build_client_response(5, 3914, %{
            "errorcode" => 1  # 通用错误
          })
          {:reply, response, state}
      end
    else
      response = build_client_response(5, 3914, %{
        "errorcode" => 1  # 通用错误
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理申请下庄请求 - 直接处理客户端原始消息
  """
  def handle_apply_off_banker(%{main_id: 5, sub_id: 3919} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端申请下庄请求: #{inspect(message.data)}")

    if state.current_room  do
      banker_message = %{"cmd" => "apply_off_banker"}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, banker_message) do
        :ok ->
          # 申请成功，客户端会收到房间的广播消息，这里不需要额外响应
          {:ok, state}

        {:error, reason} ->
          response = build_client_response(5, 3914, %{
            "errorcode" => 1  # 通用错误
          })
          {:reply, response, state}
      end
    else
      response = build_client_response(5, 3914, %{
        "errorcode" => 1  # 通用错误
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理请求上庄列表请求 - 直接处理客户端原始消息
  """
  def handle_request_banker_list(%{main_id: 5, sub_id: 3924} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端请求上庄列表请求: #{inspect(message.data)}")

    if state.current_room  do
      banker_message = %{"cmd" => "request_banker_list"}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, banker_message) do
        :ok ->
          # 请求成功，客户端会收到房间的响应消息，这里不需要额外响应
          {:ok, state}

        {:error, reason} ->
          response = build_client_response(5, 3914, %{
            "errorcode" => 1  # 通用错误
          })
          {:reply, response, state}
      end
    else
      response = build_client_response(5, 3914, %{
        "errorcode" => 1  # 通用错误
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理请求玩家列表请求 - 直接处理客户端原始消息
  """
  def handle_request_player_list(%{main_id: 5, sub_id: 3920} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端请求玩家列表请求: #{inspect(message.data)}")

    if state.current_room != nil  do
      data = message.data || %{}
      page = Map.get(data, "page", 0)

      player_message = %{"cmd" => "request_player_list", "page" => page}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, player_message) do
        :ok ->
          # 请求成功，客户端会收到房间的响应消息，这里不需要额外响应
          {:ok, state}

        {:error, reason} ->
          response = build_client_response(5, 3914, %{
            "errorcode" => 1  # 通用错误
          })
          {:reply, response, state}
      end
    else
      response = build_client_response(5, 3914, %{
        "errorcode" => 1  # 通用错误
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理续押请求 - 直接处理客户端原始消息
  """
  def handle_follow_bet(%{main_id: 5, sub_id: 3917} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端续押请求: #{inspect(message.data)}")

    if state.current_room  do
      follow_message = %{"cmd" => "follow_bet"}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, follow_message) do
        :ok ->
          # 续押成功，客户端会收到房间的广播消息，这里不需要额外响应
          {:ok, state}

        {:error, reason} ->
          response = build_client_response(5, 3914, %{
            "errorcode" => 1  # 通用错误
          })
          {:reply, response, state}
      end
    else
      response = build_client_response(5, 3914, %{
        "errorcode" => 1  # 通用错误
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理历史记录请求 - 直接处理客户端原始消息
  """
  def handle_get_history(%{main_id: 5, sub_id: 3915} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理客户端历史记录请求: #{inspect(message.data)}")

    if state.current_room  do
      data = message.data || %{}
      page = Map.get(data, "page", 0)
      count = Map.get(data, "count", 20)

      history_message = %{"cmd" => "get_history", "page" => page, "count" => count}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, history_message) do
        :ok ->
          # 请求成功，客户端会收到房间的响应消息，这里不需要额外响应
          {:ok, state}

        {:error, reason} ->
          response = build_client_response(5, 3914, %{
            "errorcode" => 1  # 通用错误
          })
          {:reply, response, state}
      end
    else
      response = build_client_response(5, 3914, %{
        "errorcode" => 1  # 通用错误
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理请求奖池信息请求
  """
  def handle_request_jackpot(%{main_id: 201, sub_id: 12} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理请求奖池信息请求: #{inspect(message.data)}")

    if state.current_room  do
      jackpot_message = %{"cmd" => "request_jackpot"}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, jackpot_message) do
        :ok ->
          response = build_response(201, 32, %{
            "code" => 0,
            "msg" => "奖池信息请求已发送"
          })
          {:reply, response, state}

        {:error, reason} ->
          response = build_response(201, 32, %{
            "code" => 1,
            "msg" => "获取奖池信息失败: #{reason}"
          })
          {:reply, response, state}
      end
    else
      response = build_response(201, 32, %{
        "code" => 1,
        "msg" => "未在龙虎斗房间中"
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理请求时间配置请求
  """
  def handle_request_time_config(%{main_id: 201, sub_id: 13} = message, state) do
    Logger.info("🐉 [LONGHU_PROTOCOL] 处理请求时间配置请求: #{inspect(message.data)}")

    if state.current_room  do
      time_message = %{"cmd" => "request_time_config"}

      case LongHuRoom.send_game_message(state.current_room, state.user_id, time_message) do
        :ok ->
          response = build_response(201, 33, %{
            "code" => 0,
            "msg" => "时间配置请求已发送"
          })
          {:reply, response, state}

        {:error, reason} ->
          response = build_response(201, 33, %{
            "code" => 1,
            "msg" => "获取时间配置失败: #{reason}"
          })
          {:reply, response, state}
      end
    else
      response = build_response(201, 33, %{
        "code" => 1,
        "msg" => "未在龙虎斗房间中"
      })
      {:reply, response, state}
    end
  end

  # 私有函数

  defp create_longhu_room(creator_id, user_info) do
    room_config = %{
      max_players: 100,  # 百人场
      # 百人场不需要最小玩家数限制
      min_players: 0,
      bet_time: 15,
      min_bet: 10,
      max_bet: 10000,
      enable_robots: true,
      robot_count: 8  # 增加机器人数量烘托气氛
    }

    RoomManager.create_room(:longhu, room_config, creator_id)
  end

  # 使用指定ID创建龙虎斗房间 (简化版本)
  # defp create_longhu_room_with_id(room_id, creator_id, user_info) do
  #   # 龙虎斗是百人场游戏，直接使用 RoomManager 的标准创建方式
  #   # RoomManager 会自动为百人场游戏创建固定ID的房间
  #   case RoomManager.create_room(:longhu, creator_id, %{}) do
  #     {:ok, created_room_id} ->
  #       Logger.info("🐉 [CREATE_ROOM] 创建房间成功 - 房间: #{created_room_id}")
  #       {:ok, created_room_id}

  #     {:error, reason} ->
  #       Logger.error("🐉 [CREATE_ROOM] 创建房间失败 - 原因: #{reason}")
  #       {:error, reason}
  #   end
  # end

  # 自动加入龙虎斗房间 (简化版本)
  # defp auto_join_longhu_room(room_id, user_id, user_info, state) do
  #   Logger.info("🐉 [AUTO_JOIN] 尝试自动加入龙虎斗房间 - 用户: #{user_id}")

  #   # 龙虎斗是百人场游戏，使用 RoomManager 的匹配功能
  #   # 这会自动找到或创建合适的房间
  #   game_id = Map.get(state, :game_id, 22)  # 龙虎斗游戏ID

  #   case RoomManager.match_room(user_id, game_id, %{}) do
  #     {:ok, match_result} ->
  #       actual_room_id = match_result.room_id
  #       new_state = Map.merge(state, %{current_room: actual_room_id, game_type: :longhu})
  #       Logger.info("🐉 [AUTO_JOIN] 自动匹配房间成功 - 房间: #{actual_room_id}")
  #       {:ok, new_state}

  #     {:error, reason} ->
  #       Logger.error("🐉 [AUTO_JOIN] 自动匹配房间失败 - 原因: #{reason}")
  #       {:error, reason}
  #   end
  # end

  # 加入房间后处理下注 (客户端协议版本)
  defp handle_bet_after_join_client(state, area, amount) do
    Logger.info("🐉 [BET_AFTER_JOIN_CLIENT] 处理自动加入后的下注 - 区域: #{area}, 金额: #{amount}")

    # 转换区域名称为原子
    area_atom = case area do
      "long" -> :long
      "hu" -> :hu
      "he" -> :he
      _ -> :long
    end

    # 使用内部消息格式，不需要协议ID (房间内部处理)
    bet_message = %{"cmd" => "bet", "area" => area_atom, "amount" => amount}

    case LongHuRoom.send_game_message(state.current_room, state.user_id, bet_message) do
      :ok ->
        # 下注成功，客户端会收到房间的广播消息，这里不需要额外响应
        {:ok, state}

      {:error, reason} ->
        response = build_client_response(5, 3914, %{
          "errorcode" => 1  # 通用错误
        })
        {:reply, response, state}
    end
  end

  defp build_response(main_id, sub_id, data) do
    %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data
    }
  end

  defp build_client_response(main_id, sub_id, data) do
    %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data,
      "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    }
  end

  @doc """
  获取协议ID映射
  """
  def get_protocol_ids(), do: @protocol_ids

  @doc """
  检查是否为龙虎斗协议
  """
  def is_longhu_protocol?(main_id, sub_id) do
    main_id == 201 and sub_id in 1..51
  end

  @doc """
  格式化游戏状态广播消息
  """
  def format_game_broadcast(event_type, data) do
    {main_id, sub_id} = Map.get(@protocol_ids, event_type, {201, 99})

    %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data,
      "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    }
  end

  @doc """
  获取客户端协议ID映射
  """
  def get_client_protocol_ids(), do: @client_protocol_ids

  @doc """
  格式化客户端协议消息 (兼容IndiaGameClient)
  """
  def format_client_message(protocol_key, data) do
    protocol_id = Map.get(@client_protocol_ids, protocol_key)

    if protocol_id do
      %{
        "mainId" => 22,  # XC主协议
        "subId" => protocol_id,
        "data" => data,
        "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
      }
    else
      Logger.error("🐉 [PROTOCOL_ERROR] 未知的客户端协议: #{protocol_key}")
      nil
    end
  end
end
