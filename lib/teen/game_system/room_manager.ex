defmodule Cypridina.Teen.GameSystem.RoomManager do
  @moduledoc """
  游戏房间管理器 - 负责房间的创建、销毁、匹配和管理

  参考IndiaGameServer的房间管理逻辑，实现：
  - 房间创建和销毁
  - 玩家匹配逻辑
  - 房间状态管理
  - 房间查找和路由
  """

  use GenServer
  require Logger

  alias Cypridina.Teen.GameSystem.Room

  @registry_name :game_room_registry

  # 游戏类型映射
  @game_types %{
    # Teen Patti
    1 => :teen_patti,
    # Rummy
    2 => :rummy,
    # Andar Bahar
    3 => :andar_bahar,
    # Dragon Tiger
    4 => :dragon_tiger,
    # 龙虎斗 (客户端使用的ID)
    22 => :longhu,
    # 龙虎斗 (协议中使用的ID)
    201 => :longhu
  }

  # 房间配置
  @room_configs %{
    teen_patti: %{
      max_players: 6,
      min_players: 2,
      # 5秒后自动开始
      auto_start_delay: 5000,
      enable_robots: true,
      robot_count: 3
    },
    longhu: %{
      max_players: 100,
      # 百人场不需要最小玩家数
      min_players: 0,
      # 1秒后自动开始
      auto_start_delay: 1000,
      enable_robots: true,
      robot_count: 8
    }
  }

  # 百人场游戏类型（只维持一个房间）
  @lobby_games [:longhu]

  # 客户端API

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  @doc """
  为玩家匹配房间
  """
  def match_room(user_id, game_id, match_data \\ %{}) do
    GenServer.call(__MODULE__, {:match_room, user_id, game_id, match_data})
  end

  @doc """
  创建新房间
  """
  def create_room(game_type, creator_id, config \\ %{}) do
    GenServer.call(__MODULE__, {:create_room, game_type, creator_id, config})
  end

  @doc """
  销毁房间
  """
  def destroy_room(room_id) do
    GenServer.call(__MODULE__, {:destroy_room, room_id})
  end

  @doc """
  获取房间信息
  """
  def get_room_info(room_id) do
    GenServer.call(__MODULE__, {:get_room_info, room_id})
  end

  @doc """
  获取所有房间统计
  """
  def get_room_stats do
    GenServer.call(__MODULE__, :get_room_stats)
  end

  @doc """
  向房间发送消息
  """
  def send_to_room(room_id, message) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        GenServer.cast(pid, message)
        :ok

      [] ->
        # 房间进程不存在，清理RoomManager中的数据
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        {:error, :room_not_found}
    end
  end

  @doc """
  调用房间方法
  """
  def call_room(room_id, message) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        GenServer.call(pid, message)

      [] ->
        # 房间进程不存在，清理RoomManager中的数据
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        {:error, :room_not_found}
    end
  end

  @doc """
  检查房间是否存在
  """
  def room_exists?(room_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{_pid, _}] ->
        true
      [] ->
        # 房间进程不存在，清理RoomManager中的数据
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        false
    end
  end

  # GenServer 回调

  @impl true
  def init(_opts) do
    Logger.info("🏠 [ROOM_MANAGER] 房间管理器启动")

    # 订阅房间事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "room_events")

    state = %{
      # room_id => room_info
      rooms: %{},
      # game_type => [room_ids]
      waiting_rooms: %{},
      # user_id => room_id
      player_rooms: %{},
      room_counter: 0,
      # 百人场房间映射 game_type => room_id
      lobby_rooms: %{}
    }

    # 为百人场游戏预创建房间
    final_state = create_lobby_rooms(state)

    {:ok, final_state}
  end

  @impl true
  def handle_call({:match_room, user_id, game_id, match_data}, _from, state) do
    Logger.info("🎯 [MATCH] 玩家 #{user_id} 请求匹配游戏 #{game_id}")

    game_type = Map.get(@game_types, game_id, :unknown)

    if game_type == :unknown do
      {:reply, {:error, :unsupported_game}, state}
    else
      case find_or_create_room(state, user_id, game_type, match_data) do
        {:ok, room_id, new_state} ->
          # 将玩家加入房间
          case add_player_to_room(room_id, user_id) do
            {:ok, :joined} ->
              # 更新玩家房间映射
              updated_state = %{
                new_state
                | player_rooms: Map.put(new_state.player_rooms, user_id, room_id)
              }

              room_info = Map.get(updated_state.rooms, room_id)

              response = %{
                room_id: room_id,
                game_type: game_type,
                seat_id: get_player_seat(room_id, user_id),
                max_players: room_info.config.max_players,
                current_players: get_room_player_count(room_id)
              }

              Logger.info("🎯 [MATCH] 匹配成功: 玩家 #{user_id} -> 房间 #{room_id}")
              {:reply, {:ok, response}, updated_state}

            {:ok, :rejoined} ->

              room_info = Map.get(new_state.rooms, room_id)
              response = %{
                room_id: room_id,
                game_type: game_type,
                seat_id: get_player_seat(room_id, user_id),
                max_players: room_info.config.max_players,
                current_players: get_room_player_count(room_id)
              }

              Logger.info("🎯 [MATCH] 重连成功: 玩家 #{user_id} -> 房间 #{room_id}")
              {:reply, {:ok, response}, new_state}

            {:error, reason} ->
              Logger.error("🎯 [MATCH] 加入房间失败: #{reason}")
              {:reply, {:error, reason}, new_state}
          end

        {:error, reason} ->
          Logger.error("🎯 [MATCH] 匹配失败: #{reason}")
          {:reply, {:error, reason}, state}
      end
    end
  end

  @impl true
  def handle_call({:create_room, game_type, creator_id, config}, _from, state) do
    case create_new_room(state, game_type, creator_id, config) do
      {:ok, room_id, new_state} ->
        {:reply, {:ok, room_id}, new_state}

      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call({:destroy_room, room_id}, _from, state) do
    case destroy_room_internal(state, room_id) do
      {:ok, new_state} ->
        {:reply, :ok, new_state}

      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call({:get_room_info, room_id}, _from, state) do
    case Map.get(state.rooms, room_id) do
      nil ->
        {:reply, {:error, :room_not_found}, state}

      room_info ->
        {:reply, {:ok, room_info}, state}
    end
  end

  @impl true
  def handle_call(:get_room_stats, _from, state) do
    stats = %{
      total_rooms: map_size(state.rooms),
      waiting_rooms: state.waiting_rooms,
      active_players: map_size(state.player_rooms)
    }

    {:reply, stats, state}
  end

  @impl true
  def handle_info({:room_terminated, room_id, reason}, state) do
    Logger.info("🏠 [ROOM_MANAGER] 收到房间终止事件: #{room_id}, 原因: #{inspect(reason)}")

    case cleanup_terminated_room(state, room_id) do
      {:ok, new_state} ->
        Logger.info("🏠 [ROOM_MANAGER] 清理终止房间成功: #{room_id}")
        {:noreply, new_state}

      {:error, reason} ->
        Logger.warning("🏠 [ROOM_MANAGER] 清理终止房间失败: #{room_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end

  @impl true
  def handle_info(msg, state) do
    Logger.debug("🏠 [ROOM_MANAGER] 未处理的info消息: #{inspect(msg)}")
    {:noreply, state}
  end

  @impl true
  def handle_cast({:cleanup_stale_room, room_id}, state) do
    Logger.info("🧹 [ROOM_MANAGER] 清理陈旧房间数据: #{room_id}")

    case cleanup_terminated_room(state, room_id) do
      {:ok, new_state} ->
        Logger.info("🧹 [ROOM_MANAGER] 清理陈旧房间成功: #{room_id}")
        {:noreply, new_state}

      {:error, reason} ->
        Logger.warning("🧹 [ROOM_MANAGER] 清理陈旧房间失败: #{room_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end

  # 私有函数

  defp cleanup_terminated_room(state, room_id) do
    case Map.get(state.rooms, room_id) do
      nil ->
        # 房间已经不在管理器中，可能已经被清理过
        Logger.debug("🏠 [ROOM_MANAGER] 房间 #{room_id} 已不在管理器中")
        {:ok, state}

      room_info ->
        # 更新状态：从所有相关数据结构中移除房间
        new_rooms = Map.delete(state.rooms, room_id)

        new_waiting_rooms =
          remove_from_waiting_list(state.waiting_rooms, room_info.game_type, room_id)

        new_player_rooms = remove_room_from_players(state.player_rooms, room_id)

        # 如果是百人场房间，也要从lobby_rooms中移除
        new_lobby_rooms =
          Enum.reduce(state.lobby_rooms, %{}, fn {game_type, lobby_room_id}, acc ->
            if lobby_room_id == room_id do
              acc
            else
              Map.put(acc, game_type, lobby_room_id)
            end
          end)

        new_state = %{
          state
          | rooms: new_rooms,
            waiting_rooms: new_waiting_rooms,
            player_rooms: new_player_rooms,
            lobby_rooms: new_lobby_rooms
        }

        Logger.info("🏠 [ROOM_MANAGER] 清理终止房间: #{room_id}, 游戏类型: #{room_info.game_type}")
        {:ok, new_state}
    end
  end

  defp find_or_create_room(state, user_id, game_type, _match_data) do
    if game_type in @lobby_games do
      # 百人场游戏：使用固定房间
      case Map.get(state.lobby_rooms, game_type) do
        nil ->
          Logger.info("🎯 [MATCH] 创建百人场房间: #{game_type}")
          create_lobby_room(state, game_type)

        room_id ->
          Logger.info("🎯 [MATCH] 使用现有百人场房间: #{room_id}")
          {:ok, room_id, state}
      end
    else
      # 普通游戏：查找或创建房间
      waiting_rooms = Map.get(state.waiting_rooms, game_type, [])

      case find_available_room(waiting_rooms, state.rooms) do
        {:ok, room_id} ->
          Logger.info("🎯 [MATCH] 找到等待房间: #{room_id}")
          {:ok, room_id, state}

        :not_found ->
          Logger.info("🎯 [MATCH] 创建新房间给玩家 #{user_id}")
          create_new_room(state, game_type, user_id, %{})
      end
    end
  end

  defp find_available_room([], _rooms), do: :not_found

  defp find_available_room([room_id | rest], rooms) do
    case Map.get(rooms, room_id) do
      nil ->
        find_available_room(rest, rooms)

      room_info ->
        current_players = get_room_player_count(room_id)

        if current_players < room_info.config.max_players do
          {:ok, room_id}
        else
          find_available_room(rest, rooms)
        end
    end
  end

  defp create_new_room(state, game_type, creator_id, custom_config) do
    room_id = generate_room_id(state)
    config = Map.get(@room_configs, game_type, %{})
    merged_config = Map.merge(config, custom_config)

    room_spec = %{
      id: room_id,
      game_type: game_type,
      creator_id: creator_id,
      config: merged_config,
      created_at: System.system_time(:millisecond),
      status: :waiting
    }

    # 根据游戏类型启动对应的房间GenServer
    room_module = case game_type do
      :longhu -> Cypridina.Teen.GameSystem.Games.LongHu.LongHuRoom
      :teen_patti -> Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiRoom
      _ -> Room  # 默认使用通用Room
    end

    case room_module.start_link(room_spec) do
      {:ok, _pid} ->
        # 更新状态
        new_rooms = Map.put(state.rooms, room_id, room_spec)
        new_waiting_rooms = add_to_waiting_list(state.waiting_rooms, game_type, room_id)

        new_state = %{
          state
          | rooms: new_rooms,
            waiting_rooms: new_waiting_rooms,
            room_counter: state.room_counter + 1
        }

        Logger.info("🏠 [ROOM_MANAGER] 创建房间成功: #{room_id} (#{game_type}) 使用模块: #{room_module}")
        {:ok, room_id, new_state}

      {:error, reason} ->
        Logger.error("🏠 [ROOM_MANAGER] 创建房间失败: #{reason}")
        {:error, reason}
    end
  end

  defp destroy_room_internal(state, room_id) do
    case Map.get(state.rooms, room_id) do
      nil ->
        {:error, :room_not_found}

      room_info ->
        # 停止房间GenServer
        case Registry.lookup(@registry_name, room_id) do
          [{pid, _}] ->
            GenServer.stop(pid)

          [] ->
            :ok
        end

        # 更新状态
        new_rooms = Map.delete(state.rooms, room_id)

        new_waiting_rooms =
          remove_from_waiting_list(state.waiting_rooms, room_info.game_type, room_id)

        new_player_rooms = remove_room_from_players(state.player_rooms, room_id)

        new_state = %{
          state
          | rooms: new_rooms,
            waiting_rooms: new_waiting_rooms,
            player_rooms: new_player_rooms
        }

        Logger.info("🏠 [ROOM_MANAGER] 销毁房间: #{room_id}")
        {:ok, new_state}
    end
  end

  defp generate_room_id(state) do
    "room_#{state.room_counter + 1}_#{:rand.uniform(9999)}"
  end

  defp add_to_waiting_list(waiting_rooms, game_type, room_id) do
    current_list = Map.get(waiting_rooms, game_type, [])
    Map.put(waiting_rooms, game_type, [room_id | current_list])
  end

  defp remove_from_waiting_list(waiting_rooms, game_type, room_id) do
    current_list = Map.get(waiting_rooms, game_type, [])
    new_list = List.delete(current_list, room_id)
    Map.put(waiting_rooms, game_type, new_list)
  end

  defp remove_room_from_players(player_rooms, room_id) do
    Enum.reject(player_rooms, fn {_user_id, r_id} -> r_id == room_id end)
    |> Enum.into(%{})
  end

  defp add_player_to_room(room_id, user_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        GenServer.call(pid, {:join_room, user_id, %{}})

      [] ->
        # 房间进程不存在，触发清理
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        {:error, :room_not_found}
    end
  end

  defp get_player_seat(room_id, user_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        case GenServer.call(pid, :get_state) do
          %{game_data: %{players: players}} ->
            case Map.get(players, user_id) do
              %{seat_id: seat_id} -> seat_id
              _ -> 1
            end

          %{players: players} ->
            case Map.get(players, user_id) do
              %{seat_id: seat_id} -> seat_id
              _ -> 1
            end

          _ ->
            1
        end

      [] ->
        # 房间进程不存在，触发清理
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        1
    end
  end

  defp get_room_player_count(room_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        case GenServer.call(pid, :get_state) do
          %{game_data: %{players: players}} -> map_size(players)
          %{players: players} -> map_size(players)
          _ -> 0
        end

      [] ->
        # 房间进程不存在，触发清理
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        0
    end
  end

  # 百人场房间管理

  defp create_lobby_rooms(state) do
    Enum.reduce(@lobby_games, state, fn game_type, acc_state ->
      case create_lobby_room(acc_state, game_type) do
        {:ok, _room_id, new_state} ->
          new_state

        {:error, reason} ->
          Logger.error("🏠 [ROOM_MANAGER] 创建百人场房间失败: #{game_type} - #{reason}")
          acc_state
      end
    end)
  end

  defp create_lobby_room(state, game_type) do
    # 为百人场游戏使用固定的server_id，与客户端配置匹配
    {game_id, server_id} =
      case game_type do
        # 与websocket_handler中的配置匹配
        :longhu -> {22, 2201}
        # 默认值
        _ -> {1, 1001}
      end

    room_id = "room_#{game_id}_#{server_id}"
    config = Map.get(@room_configs, game_type, %{})

    room_spec = %{
      id: room_id,
      game_type: game_type,
      # 系统创建的房间
      creator_id: "system",
      config: config,
      created_at: System.system_time(:millisecond),
      # 百人场房间始终处于游戏状态
      status: :playing
    }

    # 根据游戏类型启动对应的房间GenServer
    room_module = case game_type do
      :longhu -> Cypridina.Teen.GameSystem.Games.LongHu.LongHuRoom
      :teen_patti -> Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiRoom
      _ -> Room  # 默认使用通用Room
    end

    # 启动房间GenServer
    case room_module.start_link(room_spec) do
      {:ok, _pid} ->
        # 更新状态
        new_rooms = Map.put(state.rooms, room_id, room_spec)
        new_lobby_rooms = Map.put(state.lobby_rooms, game_type, room_id)

        new_state = %{
          state
          | rooms: new_rooms,
            lobby_rooms: new_lobby_rooms,
            room_counter: state.room_counter + 1
        }

        Logger.info("🏠 [ROOM_MANAGER] 创建百人场房间成功: #{room_id} (#{game_type}) 使用模块: #{room_module}")
        {:ok, room_id, new_state}

      {:error, reason} ->
        Logger.error("🏠 [ROOM_MANAGER] 创建百人场房间失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def handle_info({:cleanup_stale_room, room_id}, state) do
    Logger.info("🏠 [ROOM_MANAGER] 清理过期房间数据: #{room_id}")

    case cleanup_terminated_room(state, room_id) do
      {:ok, new_state} ->
        Logger.info("🏠 [ROOM_MANAGER] 清理成功: #{room_id}")
        {:noreply, new_state}

      {:error, reason} ->
        Logger.warning("🏠 [ROOM_MANAGER] 清理失败: #{room_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end
end
