defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Room do
  @moduledoc """
  通用房间GenServer - 根据游戏类型动态选择具体的房间实现

  这个模块作为房间的统一入口，根据游戏类型委托给具体的游戏房间实现。
  """

  use GenServer
  require Logger

  alias Cypridina.Teen.GameSystem.Games.LongHu.LongHuRoom
  alias Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiRoom

  @registry_name :game_room_registry

  def start_link(room_spec) do
    GenServer.start_link(__MODULE__, room_spec,
      name: {:via, Registry, {@registry_name, room_spec.id}}
    )
  end

  @impl true
  def init(room_spec) do
    Logger.info("🏠 [ROOM] 初始化通用房间: #{room_spec.id}, 游戏类型: #{room_spec.game_type}")

    # 通用房间的初始状态
    initial_state = %{
      id: room_spec.id,
      game_type: room_spec.game_type,
      config: room_spec.config,
      creator_id: room_spec.creator_id,
      players: %{},
      max_players: Map.get(room_spec.config, :max_players, 6),
      min_players: Map.get(room_spec.config, :min_players, 2),
      status: :waiting,
      created_at: room_spec.created_at
    }

    Logger.info("🏠 [ROOM] 通用房间初始化完成: #{room_spec.id}")
    {:ok, initial_state}
  end

  @impl true
  def handle_call(message, from, state) do
    # 委托给具体的游戏房间实现
    delegate_call(state.game_type, message, from, state)
  end

  @impl true
  def handle_cast(message, state) do
    # 委托给具体的游戏房间实现
    delegate_cast(state.game_type, message, state)
  end

  @impl true
  def handle_info(message, state) do
    # 委托给具体的游戏房间实现
    delegate_info(state.game_type, message, state)
  end

  # 私有函数 - 委托给具体的游戏实现

  defp delegate_call(:longhu, message, from, state) do
    LongHuRoom.handle_call(message, from, state)
  end

  defp delegate_call(:teen_patti, message, from, state) do
    TeenPattiRoom.handle_call(message, from, state)
  end

  defp delegate_call(game_type, _message, _from, state) do
    Logger.warning("🏠 [ROOM] 不支持的游戏类型 handle_call: #{game_type}")
    {:reply, {:error, :unsupported_game_type}, state}
  end

  defp delegate_cast(:longhu, message, state) do
    LongHuRoom.handle_cast(message, state)
  end

  defp delegate_cast(:teen_patti, message, state) do
    TeenPattiRoom.handle_cast(message, state)
  end

  defp delegate_cast(game_type, _message, state) do
    Logger.warning("🏠 [ROOM] 不支持的游戏类型 handle_cast: #{game_type}")
    {:noreply, state}
  end

  defp delegate_info(:longhu, message, state) do
    LongHuRoom.handle_info(message, state)
  end

  defp delegate_info(:teen_patti, message, state) do
    TeenPattiRoom.handle_info(message, state)
  end

  defp delegate_info(game_type, _message, state) do
    Logger.warning("🏠 [ROOM] 不支持的游戏类型 handle_info: #{game_type}")
    {:noreply, state}
  end
end
