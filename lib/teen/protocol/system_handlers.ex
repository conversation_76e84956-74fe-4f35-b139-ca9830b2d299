defmodule Cypridina.Protocol.SystemHandlers do
  @moduledoc """
  cypridina项目的系统协议处理函数模块
  处理验证码、系统状态、游戏版本等系统级协议
  参考IndiaGameServer的处理逻辑
  """

  require Logger

  # ==================== RegLogin 系统协议处理 ====================

  @doc """
  处理请求验证码 (RegLogin.CS_REQUEST_VERCODE_P)
  """
  def handle_request_vercode(%{main_id: 0, sub_id: 41} = message, state) do
    Logger.info("请求验证码: #{inspect(message.data)}")

    data = message.data || %{}
    site_id = Map.get(data, "siteid", 1)

    # 模拟验证码生成
    verification_code = %{
      "code_id" => "vercode_#{:rand.uniform(100_000)}",
      "image_data" =>
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      # 5分钟过期
      "expire_time" => System.system_time(:millisecond) + 300_000,
      "site_id" => site_id
    }

    response_data = %{
      "status" => 0,
      "verification_code" => verification_code,
      "message" => "验证码生成成功"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 42,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理响应验证码 (RegLogin.CS_RESPONSE_VERCODE_P)
  """
  def handle_response_vercode(%{main_id: 0, sub_id: 43} = message, state) do
    Logger.info("响应验证码: #{inspect(message.data)}")

    data = message.data || %{}
    code_id = Map.get(data, "code_id", "")
    user_input = Map.get(data, "user_input", "")

    # 模拟验证码验证
    # 简单验证：4位数字
    is_correct = String.length(user_input) == 4

    response_data = %{
      "status" => if(is_correct, do: 0, else: 1),
      "code_id" => code_id,
      "verified" => is_correct,
      "message" => if(is_correct, do: "验证码验证成功", else: "验证码错误")
    }

    # 根据验证结果返回不同的子协议
    sub_id = if is_correct, do: 44, else: 45

    response_map = %{
      "mainId" => 0,
      "subId" => sub_id,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求系统配置 (RegLogin.CD_REQUEST_SYSTEM_STATUS_P)
  """
  def handle_request_system_status(%{main_id: 0, sub_id: 49} = message, state) do
    Logger.info("请求系统配置: #{inspect(message.data)}")

    # 模拟系统配置信息
    system_status = %{
      # 1-正常, 0-维护
      "server_status" => 1,
      "server_version" => "2.1.0",
      "client_min_version" => "2.0.0",
      "maintenance_notice" => "",
      "update_url" => "https://example.com/update",
      "force_update" => false,
      "server_time" => System.system_time(:millisecond),
      "timezone" => "Asia/Shanghai",
      "features" => %{
        "chat_enabled" => true,
        "payment_enabled" => true,
        "ranking_enabled" => true,
        "agent_system_enabled" => true
      },
      "limits" => %{
        "max_daily_games" => 1000,
        "max_bet_amount" => 100_000,
        "min_bet_amount" => 10
      }
    }

    response_data = %{
      "status" => 0,
      "system_status" => system_status
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 50,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求游戏版本号列表 (RegLogin.CS_REQUEST_GAMEVERSIONS_P)
  """
  def handle_request_game_versions(%{main_id: 0, sub_id: 51} = message, state) do
    Logger.info("请求游戏版本号列表: #{inspect(message.data)}")

    # 模拟游戏版本列表
    game_versions = [
      %{
        "game_id" => 1001,
        "game_name" => "Teen Patti",
        "version" => "1.5.0",
        "min_version" => "1.4.0",
        "download_url" => "https://example.com/games/teenpatti.zip",
        # 15MB
        "file_size" => 15_728_640,
        "md5" => "d41d8cd98f00b204e9800998ecf8427e",
        "force_update" => false,
        "enabled" => true
      },
      %{
        "game_id" => 1002,
        "game_name" => "Rummy",
        "version" => "1.3.2",
        "min_version" => "1.3.0",
        "download_url" => "https://example.com/games/rummy.zip",
        # 12MB
        "file_size" => 12_582_912,
        "md5" => "098f6bcd4621d373cade4e832627b4f6",
        "force_update" => false,
        "enabled" => true
      },
      %{
        "game_id" => 1003,
        "game_name" => "Poker",
        "version" => "2.0.1",
        "min_version" => "2.0.0",
        "download_url" => "https://example.com/games/poker.zip",
        # 20MB
        "file_size" => 20_971_520,
        "md5" => "5d41402abc4b2a76b9719d911017c592",
        "force_update" => true,
        "enabled" => true
      }
    ]

    response_data = %{
      "status" => 0,
      "game_versions" => game_versions,
      "total_count" => length(game_versions),
      "server_time" => System.system_time(:millisecond)
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 52,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== 游戏服务器登录处理 ====================

  @doc """
  处理游戏服务器登录 (RegLogin.CS_GAMESERVER_LOGIN_P)
  """
  def handle_gameserver_login(%{main_id: 0, sub_id: 16} = message, state) do
    Logger.info("🎮 [GAME_LOGIN] 游戏服务器登录请求: #{inspect(message.data)}")

    Logger.info(
      "🎮 [GAME_LOGIN] Channel状态: #{inspect(Map.take(state, [:channel_type, :channel_info, :game_id]))}"
    )

    data = message.data || %{}
    player_id = Map.get(data, "playerid", state.user_id)
    site_id = Map.get(data, "siteid", 1)

    # 从Channel上下文获取游戏房间信息
    {server_id, game_id} =
      case Map.get(state, :channel_type) do
        :game_room ->
          channel_info = Map.get(state, :channel_info, %{})
          {Map.get(channel_info, :server_id, 0), Map.get(channel_info, :game_id, 0)}

        _ ->
          # 如果不在游戏房间Channel中，尝试从消息数据获取
          {Map.get(data, "server_id", 0), Map.get(data, "game_id", 0)}
      end

    Logger.info(
      "🎮 [GAME_LOGIN] 解析得到 - GameID: #{game_id}, ServerID: #{server_id}, PlayerID: #{player_id}"
    )

    response_data =
      cond do
        server_id <= 0 ->
          Logger.warning("🎮 [GAME_LOGIN] 登录失败: 无效的服务器ID (#{server_id})")

          %{
            "status" => 1,
            "message" => "无效的服务器ID"
          }

        game_id <= 0 ->
          Logger.warning("🎮 [GAME_LOGIN] 登录失败: 无效的游戏ID (#{game_id})")

          %{
            "status" => 2,
            "message" => "无效的游戏ID"
          }

        true ->
          Logger.info(
            "🎮 [GAME_LOGIN] 登录成功 - GameID: #{game_id}, ServerID: #{server_id}, PlayerID: #{player_id}"
          )

          # 构建响应数据，包含客户端发送的所有原始数据
          response_data =
            Map.merge(data, %{
              # 客户端检查的主要字段
              "code" => 0,
              # 错误消息（成功时为空）
              "msg" => "",
              # 0=正常登录, 1=断线重连
              "offline" => 0,
              # 确保playerid正确
              "playerid" => player_id,
              # 确保siteid正确
              "siteid" => site_id,

              # 游戏服务器信息
              "server_id" => server_id,
              "game_id" => game_id,
              "session_token" => "game_session_#{:rand.uniform(1_000_000)}",
              "server_ip" => "127.0.0.1",
              "server_port" => 8080,

              # 用户相关信息（从数据库获取）
              # 用户金币 (使用真实积分)
              "money" => Cypridina.Accounts.get_user_points_by_numberic_id(player_id),
              # 总金币 (使用真实积分)
              "totalmoney" => Cypridina.Accounts.get_user_points_by_numberic_id(player_id),
              # 昵称
              "nickname" => "Player#{player_id}",
              # 头像URL
              "headurl" => "",
              # VIP等级
              "viplevel" => 0,
              # 性别
              "sex" => 1,
              # 是否需要GPS
              "needgps" => 0,
              # 作弊通道
              "cheatchannel" => 0,

              # 控制信息
              "Control" => %{
                "ControlScore" => 0,
                "Ratio" => 0,
                "RoundCount" => 0,
                "TakeCount" => 0,
                "TakeScore" => 0,
                "ControlStatus" => 0
              }
            })

          response_data
      end

    response_map = %{
      "mainId" => 0,
      "subId" => 17,
      "data" => response_data
    }

    # 更新状态，记录登录信息
    new_state =
      Map.merge(state, %{
        game_logged_in: response_data["status"] == 0,
        game_player_id: player_id,
        game_site_id: site_id
      })

    {:reply, response_map, new_state}
  end

  # ==================== 其他系统协议处理 ====================

  @doc """
  处理服务器停机维护通知 (RegLogin.SC_SERVER_STOP_P)
  """
  def send_server_stop_notice(state, maintenance_info \\ %{}) do
    Logger.info("发送服务器停机维护通知")

    default_info = %{
      # 1小时后开始维护
      "maintenance_start" => System.system_time(:millisecond) + 3_600_000,
      # 维护2小时
      "maintenance_duration" => 7_200_000,
      "reason" => "系统升级维护",
      "compensation" => [
        %{"type" => "money", "amount" => 5000},
        %{"type" => "item", "item_id" => 1, "count" => 3}
      ]
    }

    maintenance_data = Map.merge(default_info, maintenance_info)

    response_data = %{
      "status" => 0,
      "maintenance_info" => maintenance_data,
      "message" => "服务器即将进入维护状态，请提前做好准备"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 12,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理其他地方登录通知 (RegLogin.SC_OHTER_LOGIN_P)
  """
  def send_other_login_notice(state, login_info \\ %{}) do
    Logger.info("发送其他地方登录通知")

    default_info = %{
      "login_ip" => "*************",
      "login_time" => System.system_time(:millisecond),
      "device_info" => "Android 手机",
      "location" => "北京市"
    }

    login_data = Map.merge(default_info, login_info)

    response_data = %{
      "status" => 0,
      "login_info" => login_data,
      "message" => "您的账号在其他地方登录，您已被挤下线"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 10,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理更新保存密码随机数 (RegLogin.SC_UPDATE_SAVE_RANDOM_P)
  """
  def send_update_save_random(state) do
    Logger.info("发送更新保存密码随机数")

    response_data = %{
      "status" => 0,
      "random_code" => "random_#{:rand.uniform(1_000_000)}",
      # 24小时过期
      "expire_time" => System.system_time(:millisecond) + 86_400_000,
      "message" => "密码随机数已更新"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 13,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 私有辅助函数
end
