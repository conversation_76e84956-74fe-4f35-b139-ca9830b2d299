defmodule Mix.Tasks.Cypridina.Users do
  @moduledoc """
  用户管理 Mix 任务

  ## 可用命令:

  ### 初始化超级管理员
      mix cypridina.users init_super_admin [username] [password]

  ### 创建管理员
      mix cypridina.users create_admin <super_admin_username> <username> <password> [email]

  ### 创建普通用户
      mix cypridina.users create_user <admin_username> <username> <password> [email]

  ### 创建代理
      mix cypridina.users create_agent <admin_username> <username> <password> <agent_level> [email]

  ### 列出所有用户
      mix cypridina.users list [--role=admin|user|agent]

  ### 重置用户密码
      mix cypridina.users reset_password <username> <new_password>

  ### 更新用户权限
      mix cypridina.users update_permission <username> <permission_level>

  ### 删除用户
      mix cypridina.users delete <username>

  ## 示例:

      # 初始化超级管理员
      mix cypridina.users init_super_admin super_admin admin123456

      # 创建管理员
      mix cypridina.users create_admin super_admin admin admin123 <EMAIL>

      # 创建普通用户
      mix cypridina.users create_user admin user1 password123 <EMAIL>

      # 创建根代理
      mix cypridina.users create_agent admin root_agent agent123 0 <EMAIL>

      # 列出所有管理员
      mix cypridina.users list --role=admin

      # 重置密码
      mix cypridina.users reset_password user1 newpassword123

      # 更新权限级别
      mix cypridina.users update_permission user1 1

      # 删除用户
      mix cypridina.users delete user1
  """

  use Mix.Task

  alias Cypridina.Accounts.{User, UserManagementService}
  require Ash.Query
  import Ash.Expr

  @shortdoc "用户管理命令行工具"

  def run(args) do
    Mix.Task.run("app.start")

    case args do
      ["init_super_admin" | rest] -> init_super_admin(rest)
      ["create_admin" | rest] -> create_admin(rest)
      ["create_user" | rest] -> create_user(rest)
      ["create_agent" | rest] -> create_agent(rest)
      ["list" | rest] -> list_users(rest)
      ["reset_password" | rest] -> reset_password(rest)
      ["update_permission" | rest] -> update_permission(rest)
      ["delete" | rest] -> delete_user(rest)
      _ -> show_help()
    end
  end

  defp init_super_admin(args) do
    {username, password} =
      case args do
        [username, password] -> {username, password}
        [username] -> {username, "admin123456"}
        [] -> {"super_admin", "admin123456"}
      end

    attrs = %{
      username: username,
      password: password,
      password_confirmation: password
    }

    case UserManagementService.initialize_super_admin(attrs) do
      {:ok, user} ->
        Mix.shell().info("✅ 超级管理员创建成功:")
        Mix.shell().info("   用户名: #{user.username}")
        Mix.shell().info("   数字ID: #{user.numeric_id}")
        Mix.shell().info("   权限级别: #{user.permission_level}")

      {:error, error} ->
        Mix.shell().error("❌ 超级管理员创建失败: #{inspect(error)}")
    end
  end

  defp create_admin([super_admin_username, username, password | rest]) do
    email = List.first(rest)

    with {:ok, super_admin} <- get_user_by_username(super_admin_username),
         {:ok, admin} <-
           UserManagementService.create_admin_by_super_admin(super_admin.id, %{
             username: username,
             password: password,
             password_confirmation: password,
             email: email
           }) do
      Mix.shell().info("✅ 管理员创建成功:")
      Mix.shell().info("   用户名: #{admin.username}")
      Mix.shell().info("   数字ID: #{admin.numeric_id}")
      Mix.shell().info("   权限级别: #{admin.permission_level}")
    else
      {:error, error} ->
        Mix.shell().error("❌ 管理员创建失败: #{inspect(error)}")
    end
  end

  defp create_admin(_) do
    Mix.shell().error(
      "用法: mix cypridina.users create_admin <super_admin_username> <username> <password> [email]"
    )
  end

  defp create_user([admin_username, username, password | rest]) do
    email = List.first(rest)

    with {:ok, admin} <- get_user_by_username(admin_username),
         {:ok, user} <-
           UserManagementService.create_user_by_admin(admin.id, %{
             username: username,
             password: password,
             password_confirmation: password,
             email: email
           }) do
      Mix.shell().info("✅ 用户创建成功:")
      Mix.shell().info("   用户名: #{user.username}")
      Mix.shell().info("   数字ID: #{user.numeric_id}")
      Mix.shell().info("   权限级别: #{user.permission_level}")
    else
      {:error, error} ->
        Mix.shell().error("❌ 用户创建失败: #{inspect(error)}")
    end
  end

  defp create_user(_) do
    Mix.shell().error(
      "用法: mix cypridina.users create_user <admin_username> <username> <password> [email]"
    )
  end

  defp create_agent([admin_username, username, password, agent_level | rest]) do
    email = List.first(rest)

    with {:ok, admin} <- get_user_by_username(admin_username),
         {agent_level_int, ""} <- Integer.parse(agent_level),
         {:ok, agent} <-
           UserManagementService.create_user_by_admin(admin.id, %{
             username: username,
             password: password,
             password_confirmation: password,
             email: email,
             agent_level: agent_level_int
           }) do
      Mix.shell().info("✅ 代理创建成功:")
      Mix.shell().info("   用户名: #{agent.username}")
      Mix.shell().info("   数字ID: #{agent.numeric_id}")
      Mix.shell().info("   代理级别: #{agent.agent_level}")
    else
      {:error, error} ->
        Mix.shell().error("❌ 代理创建失败: #{inspect(error)}")

      :error ->
        Mix.shell().error("❌ 代理级别必须是数字")
    end
  end

  defp create_agent(_) do
    Mix.shell().error(
      "用法: mix cypridina.users create_agent <admin_username> <username> <password> <agent_level> [email]"
    )
  end

  defp list_users(args) do
    role_filter = parse_role_filter(args)

    case User |> Ash.read() do
      {:ok, users} ->
        filtered_users = filter_users_by_role(users, role_filter)

        Mix.shell().info("=== 用户列表 ===")
        Mix.shell().info("ID\t\t数字ID\t用户名\t\t权限级别\t代理级别")
        Mix.shell().info("#{String.duplicate("-", 80)}")

        Enum.each(filtered_users, fn user ->
          Mix.shell().info(
            "#{String.slice(user.id, 0, 8)}\t#{user.numeric_id}\t#{user.username}\t\t#{user.permission_level}\t\t#{user.agent_level}"
          )
        end)

        Mix.shell().info("\n总计: #{length(filtered_users)} 个用户")

      {:error, error} ->
        Mix.shell().error("❌ 获取用户列表失败: #{inspect(error)}")
    end
  end

  defp reset_password([_username, _new_password]) do
    # 这里需要实现密码重置逻辑
    Mix.shell().info("密码重置功能待实现")
  end

  defp reset_password(_) do
    Mix.shell().error("用法: mix cypridina.users reset_password <username> <new_password>")
  end

  defp update_permission([username, permission_level]) do
    with {level, ""} <- Integer.parse(permission_level),
         {:ok, user} <- get_user_by_username(username),
         {:ok, updated_user} <-
           User
           |> Ash.Changeset.for_update(:update_permission_level, %{permission_level: level})
           |> Ash.update(user) do
      Mix.shell().info("✅ 用户权限更新成功:")
      Mix.shell().info("   用户名: #{updated_user.username}")
      Mix.shell().info("   新权限级别: #{updated_user.permission_level}")
    else
      :error ->
        Mix.shell().error("❌ 权限级别必须是数字")

      {:error, error} ->
        Mix.shell().error("❌ 权限更新失败: #{inspect(error)}")
    end
  end

  defp update_permission(_) do
    Mix.shell().error("用法: mix cypridina.users update_permission <username> <permission_level>")
  end

  defp delete_user([username]) do
    with {:ok, user} <- get_user_by_username(username),
         :ok <- User |> Ash.destroy(user) do
      Mix.shell().info("✅ 用户删除成功: #{user.username}")
    else
      {:error, error} ->
        Mix.shell().error("❌ 用户删除失败: #{inspect(error)}")
    end
  end

  defp delete_user(_) do
    Mix.shell().error("用法: mix cypridina.users delete <username>")
  end

  defp show_help do
    Mix.shell().info(@moduledoc)
  end

  # 辅助函数

  defp get_user_by_username(username) do
    case User
         |> Ash.Query.filter(expr(username == ^username))
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, [user]} -> {:ok, user}
      {:ok, []} -> {:error, "用户不存在: #{username}"}
      {:error, error} -> {:error, error}
    end
  end

  defp parse_role_filter(args) do
    Enum.find_value(args, :all, fn arg ->
      case String.split(arg, "=") do
        ["--role", role] -> String.to_atom(role)
        _ -> nil
      end
    end)
  end

  defp filter_users_by_role(users, :all), do: users
  defp filter_users_by_role(users, :admin), do: Enum.filter(users, &(&1.permission_level >= 1))
  defp filter_users_by_role(users, :user), do: Enum.filter(users, &(&1.permission_level == 0))
  defp filter_users_by_role(users, :agent), do: Enum.filter(users, &(&1.agent_level >= 0))
  defp filter_users_by_role(users, _), do: users
end
