defmodule CypridinaWeb.Live.UserAssetLive do
  @moduledoc """
  用户资产管理界面
  """
  use Backpex.LiveResource,
    fluid?: true,
    adapter_config: [
      schema: Cypridina.Ecto.Accounts.EUserAsset,
      repo: Cypridina.Repo,
      update_changeset: &__MODULE__.update_changeset/3,
      create_changeset: &__MODULE__.create_changeset/3
    ],
    layout: {CypridinaWeb.Layouts, :admin}

  require Logger
  alias Cypridina.Ecto.Accounts.{EUser, EUserAsset}
  import Ecto.Query

  @impl Backpex.LiveResource
  def singular_name, do: "用户资产"

  @impl Backpex.LiveResource
  def plural_name, do: "用户资产管理"


  # 没有新建功能
  @impl Backpex.LiveResource
  def can?(_assigns, :create, _item), do: false
  @impl Backpex.LiveResource
  def can?(_assigns, :index, _item), do: true
  @impl Backpex.LiveResource
  def can?(_assigns, :edit, _item), do: true
  @impl Backpex.LiveResource
  def can?(_assigns, _action, _item), do: false


  @impl Backpex.LiveResource
  def fields do
    [
      user: %{
        module: Backpex.Fields.BelongsTo,
        label: "用户",
        display_field: :username,
        live_resource: CypridinaWeb.Live.UserLive,
        # options_query: &user_query/2,
      },
      points: %{
        module: Backpex.Fields.Number,
        label: "积分",
        # 添加输入验证和帮助文本
        # validation_message: "积分必须为非负整数",
        help_text: "用户的游戏积分余额，用于投注和游戏",
        index_editable: true,
      },
      commission: %{
        module: Backpex.Fields.Number,
        label: "抽水",
        # 添加输入验证和帮助文本
        # validation_message: "抽水必须为非负整数",
        help_text: "代理系统的佣金收入，与积分分离管理"
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
      }
    ]
  end

  @impl Backpex.LiveResource
  def panels do
    [
      main: "用户资产信息"
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(_assigns) do
    [
      edit: %{
        module: Backpex.ItemActions.Edit,
        label: "编辑资产",
        icon: "hero-pencil-square",
        confirm: "确定要编辑此用户的资产吗？请谨慎操作。"
      },
      # show: %{
      #   module: Backpex.ItemActions.Show,
      #   label: "查看详情",
      #   icon: "hero-eye"
      # }
    ]
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :index, :before_main) do
    # 获取资产统计信息并添加到assigns中
    assigns = assign(assigns, :stats, get_asset_statistics())

    ~H"""
    <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 总积分统计 -->
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span class="text-green-600 font-bold">💰</span>
            </div>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-green-800">总积分</p>
            <p class="text-lg font-semibold text-green-900"><%= format_number(@stats.total_points) %></p>
          </div>
        </div>
      </div>

      <!-- 总抽水统计 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="text-blue-600 font-bold">💎</span>
            </div>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-blue-800">总抽水</p>
            <p class="text-lg font-semibold text-blue-900"><%= format_number(@stats.total_commission) %></p>
          </div>
        </div>
      </div>

      <!-- 用户数量统计 -->
      <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span class="text-purple-600 font-bold">👥</span>
            </div>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-purple-800">用户数量</p>
            <p class="text-lg font-semibold text-purple-900"><%= @stats.user_count %></p>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-4 p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold text-blue-800 mb-2">📊 用户资产管理</h3>
      <ul class="text-sm text-blue-700 space-y-1">
        <li>• 管理所有用户的积分和抽水资产</li>
        <li>• <strong>积分</strong>：用于游戏投注的主要货币</li>
        <li>• <strong>抽水</strong>：代理系统的佣金收入，与积分分离管理</li>
        <li>• 点击"编辑资产"按钮可以修改用户的积分和抽水</li>
        <li>• 所有修改操作都会被记录到系统日志中</li>
      </ul>
    </div>
    """
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :form, :before_form) do
    ~H"""
    <div class="mb-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
      <h4 class="text-md font-semibold text-yellow-800 mb-2">⚠️ 编辑用户资产</h4>
      <div class="text-sm text-yellow-700 space-y-2">
        <p><strong>重要提醒：</strong></p>
        <ul class="list-disc list-inside space-y-1 ml-2">
          <li>所有资产变动都会被系统记录，请谨慎操作</li>
          <li><strong>积分</strong>：用户的游戏货币，用于投注和游戏消费</li>
          <li><strong>抽水</strong>：代理系统的佣金收入，与积分分离管理</li>
          <li>修改后的数值必须为非负整数</li>
          <li>建议在修改前记录原因和用途</li>
        </ul>
      </div>
    </div>
    """
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :form, :after_form) do
    ~H"""
    <div class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
      <h4 class="text-md font-semibold text-blue-800 mb-2">📝 操作说明</h4>
      <div class="text-sm text-blue-700 space-y-1">
        <p>• 点击"保存"按钮提交修改</p>
        <p>• 系统会自动验证数值的有效性</p>
        <p>• 修改成功后会自动更新"更新时间"字段</p>
        <p>• 如需取消修改，请点击"取消"按钮</p>
      </div>
    </div>
    """
  end

  # 用户查询函数
  # defp user_query(_schema, search) do
  #   search = "%#{search}%"

  #   from(u in EUser,
  #     where:
  #       ilike(u.username, ^search) or
  #         ilike(fragment("CAST(? AS TEXT)", u.numeric_id), ^search),
  #     order_by: [asc: u.username],
  #     limit: 20
  #   )
  # end

  # 获取资产统计信息 (公开用于测试)
  @spec get_asset_statistics() :: %{
          total_commission: any(),
          total_points: any(),
          user_count: any()
        }
  def get_asset_statistics do
    alias Cypridina.Repo

    stats_query = from(ua in EUserAsset,
      select: %{
        total_points: sum(ua.points),
        total_commission: sum(ua.commission),
        user_count: count(ua.id)
      }
    )

    case Repo.one(stats_query) do
      nil ->
        %{total_points: 0, total_commission: 0, user_count: 0}
      stats ->
        %{
          total_points: stats.total_points || 0,
          total_commission: stats.total_commission || 0,
          user_count: stats.user_count || 0
        }
    end
  end

  # 数字格式化函数 (公开用于测试)
  def format_number(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  def format_number(number) when is_nil(number), do: "0"
  def format_number(number), do: to_string(number)

  # @impl Backpex.LiveResource
  # def can?(assigns, action, item \\ nil)

  # BackPex 需要的 changeset 函数
  def create_changeset(schema, attrs, _metadata) do
    EUserAsset.create_changeset(schema, attrs)
  end

  def update_changeset(schema, attrs, metadata) do

    # 获取实际的 schema 数据
    actual_schema = case schema do
      %Ecto.Changeset{data: data} -> data
      data -> data
    end

    # 记录修改操作
    current_user = get_in(metadata, [:assigns, :current_user])
    user_info = if current_user, do: "#{current_user.username}(#{current_user.id})", else: "未知用户"

    Logger.info("用户资产修改 - 操作者: #{user_info}, 目标用户资产ID: #{actual_schema.id}, 修改内容: #{inspect(attrs)}")

    # 记录原始值用于对比
    original_points = actual_schema.points
    original_commission = actual_schema.commission

    changeset = EUserAsset.changeset(actual_schema, attrs)

    # 如果changeset有效，记录具体变更
    if changeset.valid? do
      new_points = Ecto.Changeset.get_change(changeset, :points, original_points)
      new_commission = Ecto.Changeset.get_change(changeset, :commission, original_commission)

      if new_points != original_points do
        Logger.info("积分变更: #{original_points} -> #{new_points} (变化: #{new_points - original_points})")
      end

      if new_commission != original_commission do
        Logger.info("抽水变更: #{original_commission} -> #{new_commission} (变化: #{new_commission - original_commission})")
      end
    else
      Logger.warning("用户资产修改失败 - 验证错误: #{inspect(changeset.errors)}")
    end

    changeset
  end
end
