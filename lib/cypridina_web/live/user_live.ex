defmodule CypridinaWeb.Live.UserLive do
  use Backpex.LiveResource,
    adapter_config: [
      schema: Cypridina.Ecto.Accounts.EUser,
      repo: Cypridina.Repo,
      update_changeset: &__MODULE__.update_changeset/3,
      create_changeset: &__MODULE__.create_changeset/3,
      item_query: &__MODULE__.item_query/3
    ],
    layout: {CypridinaWeb.Layouts, :admin},
    init_order: &__MODULE__.init_order/1

  import Ecto.Query, warn: false
  alias Cypridina.Ecto.Accounts.{EUser, EAgentRelationship}
  alias Cypridina.Repo

  @impl Backpex.LiveResource
  def singular_name, do: "用户"

  @impl Backpex.LiveResource
  def plural_name, do: "用户"

  @impl Backpex.LiveResource
  def can?(assigns, :create, _item) do
    assigns.current_user.is_admin
  end

  @impl Backpex.LiveResource
  def can?(_assigns, _action, _item), do: true

  def item_query(query, _live_action, assigns) do
    current_user = assigns.current_user

    cond do
      # 管理员看所有用户
      current_user.permission_level >= 2 ->
        query

      # 代理看自己的下线和上级
      current_user.agent_level >= 0 ->
        agent_filter_query(query, current_user.id)

      # 普通用户只能看自己
      true ->
        from(u in query, where: u.id == ^current_user.id)
    end
  end

  def init_order(_assigns) do
    %{by: :numeric_id, direction: :asc}
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      create_subordinate: %{module: CypridinaWeb.ResourceActions.CreateSubordinate}
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(default_actions) do
    default_actions
    |> Enum.concat([
      transfer_points: %{module: CypridinaWeb.ItemActions.TransferPoints},
      admin_adjust_points: %{
        module: CypridinaWeb.ItemActions.AdminAdjustPoints,
        # 只有超级管理员可以看到此操作
        can?: fn assigns ->
          assigns.current_user && assigns.current_user.permission_level >= 2
        end
      }
    ])
  end

  @impl Backpex.LiveResource
  def fields do
    [
      username: %{
        module: Backpex.Fields.Text,
        label: "用户账号",
        readonly: true,
        searchable: true
      },
      agent_level: %{
        module: Backpex.Fields.Number,
        label: "代理等级",
        # readonly: true,
        render: fn assigns ->
          assigns =
            assign(
              assigns,
              :display_text,
              case assigns.value do
                -1 -> "普通用户"
                0 -> "根代理"
                level when level > 0 -> "下级代理 (L#{level})"
                _ -> "未知"
              end
            )

          ~H"<span>{@display_text}</span>"
        end
      },
      permission_level: %{
        module: Backpex.Fields.Number,
        label: "身份",
        # readonly: true,
        render: fn assigns ->
          assigns =
            assign(
              assigns,
              :display_text,
              case assigns.value do
                level when level == 2 -> "超级管理员"
                level when level == 1 -> "管理员"
                0 -> "普通用户"
                _ -> "其他"
              end
            )

          ~H"<span>{@display_text}</span>"
        end
      },
      subordinate_relationships: %{
        module: Backpex.Fields.HasMany,
        label: "抽水比例",
        display_field: :commission_rate,
        # except: [:index],
        orderable: false,
        searchable: false,
        live_resource: CypridinaWeb.Live.AgentLive
      },
      asset: %{
        module: Backpex.Fields.BelongsTo,
        label: "用户积分",
        display_field: :points,
        can?: fn assigns -> assigns.current_user.permission_level == 2 end,
        live_resource: CypridinaWeb.Live.UserAssetLive
      }
    ]
  end

  # 代理查询过滤器 - 显示自己的下线和上级
  defp agent_filter_query(query, agent_id) do
    superior_id = get_agent_superior(agent_id)

    if superior_id do
      from(u in query,
        # 自己
        # 自己的下线
        # 自己的上级（创建自己的代理）
        where:
          u.id == ^agent_id or
            u.created_by_agent == ^agent_id or
            u.id == ^superior_id
      )
    else
      from(u in query,
        # 自己
        # 自己的下线
        where:
          u.id == ^agent_id or
            u.created_by_agent == ^agent_id
      )
    end
  end

  # 获取代理的上级
  defp get_agent_superior(agent_id) do
    case EUser |> Repo.get(agent_id) do
      %{created_by_agent: superior_id} when not is_nil(superior_id) -> superior_id
      _ -> nil
    end
  end

  # BackPex 需要的 changeset 函数
  def create_changeset(schema, attrs, _metadata) do
    EUser.registration_changeset(schema, attrs)
  end

  def update_changeset(schema, attrs, metadata) do
    # 如果更新抽水比例，需要特殊处理
    if Map.has_key?(attrs, "commission_rate") do
      update_commission_rate(schema, attrs, metadata)
    else
      EUser.changeset(schema, attrs)
    end
  end

  # 更新抽水比例的特殊处理
  defp update_commission_rate(schema, attrs, _metadata) do
    commission_rate = Map.get(attrs, "commission_rate", 0.0)

    # 更新代理关系中的抽水比例
    case EAgentRelationship
         |> where([ar], ar.subordinate_id == ^schema.id and ar.status == 1)
         |> Repo.one() do
      %EAgentRelationship{} = relationship ->
        EAgentRelationship.changeset(relationship, %{commission_rate: commission_rate})
        |> Repo.update()

      nil ->
        # 如果没有代理关系，返回原始 changeset
        EUser.changeset(schema, Map.delete(attrs, "commission_rate"))
    end

    # 返回用户的 changeset（不包含 commission_rate 字段）
    EUser.changeset(schema, Map.delete(attrs, "commission_rate"))
  end
end
