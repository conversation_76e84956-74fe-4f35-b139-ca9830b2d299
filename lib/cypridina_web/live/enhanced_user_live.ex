defmodule CypridinaWeb.Live.EnhancedUserLive do
  @moduledoc """
  增强版用户管理LiveView

  展示如何在统一的管理后台中根据用户权限显示不同内容：
  - 超级管理员：可以管理所有用户，包括其他管理员
  - 管理员：可以管理普通用户和代理，但不能管理其他管理员
  - 代理：只能管理自己的下级用户
  """

  use CypridinaWeb, :live_view

  alias CypridinaWeb.{AuthHelper, PermissionComponents}
  alias Cypridina.Accounts.{User, AdminService, AgentService}

  import CypridinaWeb.PermissionComponents

  def mount(_params, _session, socket) do
    # 权限已经在 on_mount 中检查
    user = socket.assigns.current_user
    role = socket.assigns.user_role

    # 根据权限获取用户列表
    users = get_users_by_permission(user, role)

    socket =
      socket
      |> assign(:users, users)
      |> assign(:page_title, "用户管理")
      |> assign(:selected_user, nil)
      |> assign(:show_create_form, false)

    {:ok, socket}
  end

  def handle_event("select_user", %{"user_id" => user_id}, socket) do
    case User |> Ash.get(user_id) do
      {:ok, user} ->
        {:noreply, assign(socket, :selected_user, user)}

      _ ->
        {:noreply, put_flash(socket, :error, "用户不存在")}
    end
  end

  def handle_event("show_create_form", _params, socket) do
    {:noreply, assign(socket, :show_create_form, true)}
  end

  def handle_event("hide_create_form", _params, socket) do
    {:noreply, assign(socket, :show_create_form, false)}
  end

  def handle_event("delete_user", %{"user_id" => user_id}, socket) do
    current_user = socket.assigns.current_user

    case AuthHelper.require_permission(current_user, :super_admin, fn ->
           case User |> Ash.get(user_id) do
             {:ok, user} ->
               User |> Ash.destroy(user)

             error ->
               error
           end
         end) do
      {:ok, _} ->
        users = get_users_by_permission(current_user, socket.assigns.user_role)

        socket =
          socket
          |> assign(:users, users)
          |> assign(:selected_user, nil)
          |> put_flash(:info, "用户删除成功")

        {:noreply, socket}

      {:error, :insufficient_permissions} ->
        {:noreply, put_flash(socket, :error, "只有超级管理员才能删除用户")}

      {:error, _reason} ->
        {:noreply, put_flash(socket, :error, "删除用户失败")}
    end
  end

  def handle_event("update_permission", %{"user_id" => user_id, "level" => level}, socket) do
    current_user = socket.assigns.current_user
    level = String.to_integer(level)

    case AuthHelper.require_permission(current_user, :super_admin, fn ->
           case User |> Ash.get(user_id) do
             {:ok, user} ->
               user
               |> Ash.Changeset.for_update(:update_permission_level, %{permission_level: level})
               |> Ash.update()

             error ->
               error
           end
         end) do
      {:ok, _updated_user} ->
        users = get_users_by_permission(current_user, socket.assigns.user_role)

        socket =
          socket
          |> assign(:users, users)
          |> put_flash(:info, "权限更新成功")

        {:noreply, socket}

      {:error, :insufficient_permissions} ->
        {:noreply, put_flash(socket, :error, "只有超级管理员才能修改用户权限")}

      {:error, _reason} ->
        {:noreply, put_flash(socket, :error, "权限更新失败")}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="enhanced-user-management">
      <!-- 页面标题和权限信息 -->
      <div class="header mb-6">
        <div class="flex justify-between items-center">
          <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
          <.permission_badge user={@current_user} />
        </div>
        
    <!-- 权限说明 -->
        <.permission_info user={@current_user} class="mt-4" />
      </div>
      
    <!-- 操作按钮 -->
      <div class="actions mb-6">
        <div class="flex space-x-4">
          <!-- 创建用户按钮 - 代理和管理员都可以创建用户 -->
          <.permission_required user={@current_user} permission={:admin_or_agent}>
            <button
              phx-click="show_create_form"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              创建用户
            </button>
          </.permission_required>
          
    <!-- 刷新按钮 -->
          <button
            phx-click="refresh"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            刷新列表
          </button>
        </div>
      </div>
      
    <!-- 用户列表 -->
      <div class="user-list bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <%= if @can_view_all_data do %>
              所有用户列表
            <% else %>
              我的下级用户列表
            <% end %>
          </h3>

          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    用户信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    权限级别
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    代理级别
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr :for={user <- @users} class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span class="text-sm font-medium text-gray-700">
                            {String.first(user.username) |> String.upcase()}
                          </span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{user.username}</div>
                        <div class="text-sm text-gray-500">ID: {user.numeric_id}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <.permission_badge user={user} />
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= case user.agent_level do %>
                      <% -1 -> %>
                        普通用户
                      <% 0 -> %>
                        根代理
                      <% level -> %>
                        {level}级代理
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= if user.inserted_at do %>
                      {Calendar.strftime(user.inserted_at, "%Y-%m-%d %H:%M")}
                    <% else %>
                      未知
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <!-- 查看详情 - 所有有权限的用户都可以查看 -->
                    <button
                      phx-click="select_user"
                      phx-value-user_id={user.id}
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      查看
                    </button>
                    
    <!-- 编辑用户 - 需要能管理该用户 -->
                    <.can_manage user={@current_user} target_user_id={user.id}>
                      <button
                        phx-click="edit_user"
                        phx-value-user_id={user.id}
                        class="text-blue-600 hover:text-blue-900"
                      >
                        编辑
                      </button>
                    </.can_manage>
                    
    <!-- 修改权限 - 只有超级管理员可以 -->
                    <.permission_required user={@current_user} permission={:super_admin}>
                      <select
                        phx-change="update_permission"
                        phx-value-user_id={user.id}
                        name="level"
                        class="text-xs border-gray-300 rounded"
                      >
                        <option value="0" selected={user.permission_level == 0}>普通用户</option>
                        <option value="1" selected={user.permission_level == 1}>管理员</option>
                        <option value="2" selected={user.permission_level == 2}>超级管理员</option>
                      </select>
                    </.permission_required>
                    
    <!-- 删除用户 - 只有超级管理员可以 -->
                    <.permission_required user={@current_user} permission={:super_admin}>
                      <button
                        phx-click="delete_user"
                        phx-value-user_id={user.id}
                        data-confirm="确定要删除这个用户吗？"
                        class="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </.permission_required>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
    <!-- 用户详情侧边栏 -->
      <div :if={@selected_user} class="user-details mt-6">
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">用户详情</h3>

          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">用户名</dt>
              <dd class="mt-1 text-sm text-gray-900">{@selected_user.username}</dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">数字ID</dt>
              <dd class="mt-1 text-sm text-gray-900">{@selected_user.numeric_id}</dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">权限级别</dt>
              <dd class="mt-1">
                <.permission_badge user={@selected_user} />
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">代理级别</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= case @selected_user.agent_level do %>
                  <% -1 -> %>
                    普通用户
                  <% 0 -> %>
                    根代理
                  <% level -> %>
                    {level}级代理
                <% end %>
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">创建时间</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= if @selected_user.inserted_at do %>
                  {Calendar.strftime(@selected_user.inserted_at, "%Y-%m-%d %H:%M:%S")}
                <% else %>
                  未知
                <% end %>
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">最后更新</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= if @selected_user.updated_at do %>
                  {Calendar.strftime(@selected_user.updated_at, "%Y-%m-%d %H:%M:%S")}
                <% else %>
                  未知
                <% end %>
              </dd>
            </div>
          </dl>
          
    <!-- 管理操作 -->
          <div class="mt-6 flex space-x-3">
            <.can_manage user={@current_user} target_user_id={@selected_user.id}>
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                编辑用户
              </button>
            </.can_manage>

            <.permission_required user={@current_user} permission={:super_admin}>
              <button
                phx-click="delete_user"
                phx-value-user_id={@selected_user.id}
                data-confirm="确定要删除这个用户吗？"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                删除用户
              </button>
            </.permission_required>

            <button
              phx-click="select_user"
              phx-value-user_id=""
              class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
            >
              关闭详情
            </button>
          </div>
        </div>
      </div>
      
    <!-- 权限不足提示 -->
      <.conditional condition={length(@users) == 0}>
        <:if_true>
          <.permission_warning message="您当前没有可管理的用户，或者没有查看用户的权限。" />
        </:if_true>
      </.conditional>
    </div>
    """
  end

  # 根据用户权限获取用户列表
  defp get_users_by_permission(user, role) do
    case role do
      role when role in [:super_admin, :admin] ->
        # 管理员可以看到所有用户
        case User |> Ash.read() do
          {:ok, users} -> users
          _ -> []
        end

      role when role in [:root_agent, :agent] ->
        # 代理只能看到自己的下级用户
        AgentService.get_direct_subordinates(user.id)

      _ ->
        # 其他情况返回空列表
        []
    end
  end
end
