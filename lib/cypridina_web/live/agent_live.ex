defmodule CypridinaWeb.Live.AgentLive do
  @moduledoc """
  代理管理界面 - 代理可以创建下线账号和设置抽水比例
  """
  use CypridinaWeb.Live.AgentRestrictedLive,
    adapter_config: [
      schema: Cypridina.Ecto.Accounts.EAgentRelationship,
      repo: Cypridina.Repo,
      update_changeset: &__MODULE__.update_changeset/3,
      create_changeset: &__MODULE__.create_changeset/3
    ],
    layout: {CypridinaWeb.Layouts, :admin}

  alias Cypridina.Ecto.Accounts.{EUser, EAgentRelationship}
  alias Cypridina.Accounts.{AdminService, AgentService}
  import Ecto.Query
  require Logger

  @impl Backpex.LiveResource
  def singular_name, do: "代理关系"

  @impl Backpex.LiveResource
  def plural_name, do: "代理关系管理"

  @impl Backpex.LiveResource
  def can?(_assigns, :index, _item), do: true

  @impl Backpex.LiveResource
  def can?(_assigns, _action, _item), do: false

  @impl Backpex.LiveResource
  def fields do
    [
      agent: %{
        module: Backpex.Fields.BelongsTo,
        label: "代理",
        display_field: :username,
        live_resource: CypridinaWeb.Live.UserLive,
        options_query: fn query, _assigns ->
          from u in query,
            where: u.agent_level >= 0,
            order_by: [asc: u.username]
        end
      },
      subordinate: %{
        module: Backpex.Fields.BelongsTo,
        label: "下线",
        display_field: :username,
        live_resource: CypridinaWeb.Live.UserLive,
        options_query: fn query, _assigns ->
          from u in query,
            order_by: [asc: u.username]
        end
      },
      # level: %{
      #   module: Backpex.Fields.Number,
      #   label: "层级"
      # },
      commission_rate: %{
        module: Backpex.Fields.Number,
        label: "抽水比例"
        # type: :decimal,
        # precision: 4,
        # scale: 2
      },
      # status: %{
      #   module: Backpex.Fields.Select,
      #   label: "状态",
      #   options: [
      #     {1, "有效"},
      #     {0, "无效"}
      #   ]
      # },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间"
      }
    ]
  end

  @impl Backpex.LiveResource
  def panels do
    [
      main: "基本信息"
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions(default_actions) do
    Logger.info("resource_actions: #{inspect(default_actions)}")

    [
      # create_subordinate: %{module: CypridinaWeb.ResourceActions.CreateSubordinate}
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(default_actions) do
    default_actions
    |> Keyword.drop([:delete])
  end

  def before_changeset(changeset, attrs, metadata, repo, field, assigns) do
    Logger.info("before_changeset: #{inspect(changeset)}")
    Logger.info("attrs: #{inspect(attrs)}")
    Logger.info("metadata: #{inspect(metadata)}")
    Logger.info("repo: #{inspect(repo)}")
    Logger.info("field: #{inspect(field)}")
    Logger.info("assigns: #{inspect(assigns)}")

    changeset
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :index, :before_main) do
    ~H"""
    <div class="mb-4 p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold text-blue-800 mb-2">代理系统说明</h3>
      <ul class="text-sm text-blue-700 space-y-1">
        <li>• 代理可以创建下线账号并设置抽水比例</li>
        <li>• 抽水比例范围：0% - 100%</li>
        <li>• 下线的投注和股票交易将按设定比例抽水给代理</li>
      </ul>
    </div>
    """
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :form, :before_form) do
    ~H"""
    <div class="mb-4 p-4 bg-yellow-50 rounded-lg">
      <h4 class="text-md font-semibold text-yellow-800 mb-2">创建代理关系</h4>
      <p class="text-sm text-yellow-700">
        请选择代理和下线用户，并设置合适的抽水比例。
      </p>
    </div>
    """
  end



  # 自定义 changeset 函数
  def create_changeset(schema, attrs, _metadata) do
    schema
    |> Ecto.Changeset.cast(attrs, [:agent_id, :subordinate_id, :level, :commission_rate, :status])
    |> Ecto.Changeset.validate_required([:agent_id, :subordinate_id])
    |> Ecto.Changeset.validate_number(:commission_rate,
      greater_than_or_equal_to: 0,
      less_than_or_equal_to: 1
    )
    |> validate_not_self_agent()
    |> validate_unique_relationship()
  end

  def update_changeset(schema, attrs, _metadata) do
    schema
    |> Ecto.Changeset.cast(attrs, [:level, :commission_rate, :status])
    |> Ecto.Changeset.validate_number(:commission_rate,
      greater_than_or_equal_to: 0,
      less_than_or_equal_to: 1
    )
  end

  # 验证不能自己做自己的代理
  defp validate_not_self_agent(changeset) do
    agent_id = Ecto.Changeset.get_field(changeset, :agent_id)
    subordinate_id = Ecto.Changeset.get_field(changeset, :subordinate_id)

    if agent_id && subordinate_id && agent_id == subordinate_id do
      Ecto.Changeset.add_error(changeset, :subordinate_id, "不能将自己设为下线")
    else
      changeset
    end
  end

  # 验证代理关系的唯一性
  defp validate_unique_relationship(changeset) do
    agent_id = Ecto.Changeset.get_field(changeset, :agent_id)
    subordinate_id = Ecto.Changeset.get_field(changeset, :subordinate_id)

    if agent_id && subordinate_id do
      case Cypridina.Repo.get_by(EAgentRelationship,
             agent_id: agent_id,
             subordinate_id: subordinate_id
           ) do
        nil -> changeset
        _existing -> Ecto.Changeset.add_error(changeset, :subordinate_id, "该代理关系已存在")
      end
    else
      changeset
    end
  end

  # 实现代理权限控制
  @impl CypridinaWeb.Live.AgentRestrictedLive
  def can_view_item?(user, item) do
    # 管理员可以查看所有项目
    if AdminService.is_admin?(user) do
      true
    else
      # 代理只能查看自己的代理关系
      item.agent_id == user.id
    end
  end

  @impl CypridinaWeb.Live.AgentRestrictedLive
  def can_edit_item?(user, item) do
    # 管理员可以编辑所有项目
    if AdminService.is_admin?(user) do
      true
    else
      # 代理只能编辑自己的代理关系
      item.agent_id == user.id
    end
  end

  @impl CypridinaWeb.Live.AgentRestrictedLive
  def agent_filters(socket) do
    user = socket.assigns[:current_user]

    if user && AgentService.is_agent?(user) do
      # 代理只能看到自己的代理关系
      [agent_id: user.id]
    else
      []
    end
  end
end
