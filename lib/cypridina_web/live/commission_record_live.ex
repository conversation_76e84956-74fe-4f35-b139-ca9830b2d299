defmodule CypridinaWeb.Live.CommissionRecordLive do
  @moduledoc """
  抽水记录管理界面
  """
  use Backpex.LiveResource,
    adapter_config: [
      schema: Cypridina.Ecto.Accounts.ECommissionRecord,
      repo: Cypridina.Repo,
      update_changeset: &__MODULE__.update_changeset/3,
      create_changeset: &__MODULE__.create_changeset/3
    ],
    layout: {CypridinaWeb.Layouts, :admin}

  alias Cypridina.Ecto.Accounts.ECommissionRecord

  @impl Backpex.LiveResource
  def singular_name, do: "抽水记录"

  @impl Backpex.LiveResource
  def plural_name, do: "抽水记录管理"

  @impl Backpex.LiveResource
  def fields do
    [
      agent: %{
        module: Backpex.Fields.BelongsTo,
        label: "代理",
        display_field: :username,
        live_resource: CypridinaWeb.Live.UserLive
      },
      subordinate: %{
        module: Backpex.Fields.BelongsTo,
        label: "下线",
        display_field: :username,
        live_resource: CypridinaWeb.Live.UserLive
      },
      transaction_type: %{
        module: Backpex.Fields.Select,
        label: "交易类型",
        options: [
          {"bet", "投注"},
          {"stock_buy", "买入股票"},
          {"stock_sell", "卖出股票"}
        ]
      },
      transaction_id: %{
        module: Backpex.Fields.Text,
        label: "交易ID"
      },
      original_amount: %{
        module: Backpex.Fields.Number,
        label: "原始金额"
      },
      commission_rate: %{
        module: Backpex.Fields.Number,
        label: "抽水比例 (%)"
        # type: :decimal,
        # precision: 4,
        # scale: 2
      },
      commission_amount: %{
        module: Backpex.Fields.Number,
        label: "抽水金额"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {1, "有效"},
          {0, "无效"}
        ]
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间"
      }
    ]
  end

  @impl Backpex.LiveResource
  def panels do
    [
      main: "抽水记录详情"
    ]
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :index, :before_main) do
    ~H"""
    <div class="mb-4 p-4 bg-green-50 rounded-lg">
      <h3 class="text-lg font-semibold text-green-800 mb-2">抽水记录说明</h3>
      <ul class="text-sm text-green-700 space-y-1">
        <li>• 记录所有代理从下线获得的抽水收益</li>
        <li>• 包括投注抽水、股票买入抽水、股票卖出抽水</li>
        <li>• 抽水金额会自动添加到代理的积分账户</li>
      </ul>
    </div>
    """
  end

  # 只读模式 - 抽水记录不允许手动创建或编辑
  @impl Backpex.LiveResource
  def can?(assigns, action, item \\ nil)

  def can?(_assigns, :index, _item), do: true
  def can?(_assigns, :show, _item), do: true
  def can?(_assigns, _action, _item), do: false

  # 虽然不允许创建和编辑，但需要提供 changeset 函数
  def create_changeset(schema, attrs, _metadata) do
    schema
    |> Ecto.Changeset.cast(attrs, [])
  end

  def update_changeset(schema, attrs, _metadata) do
    schema
    |> Ecto.Changeset.cast(attrs, [])
  end
end
