defmodule CypridinaWeb.Live.ProfileLive do
  @moduledoc """
  个人信息页面 - 显示用户基本信息、上线信息、退费功能、修改密码
  """
  use CypridinaWeb, :live_view

  alias Cypridina.Accounts.{User, AgentRelationship}
  alias Cypridina.RacingGame.PointsTransaction
  require Ash.Query

  def mount(_params, _session, socket) do
    current_user = socket.assigns.current_user

    # 获取用户详细信息
    user_with_asset = User.get_by_id!(current_user.id, load: [:asset])
    
    # 获取上线信息
    superior_info = get_superior_info(current_user.id)
    
    # 获取退费记录
    refund_transactions = get_refund_transactions(current_user.id)

    socket =
      socket
      |> assign(:page_title, "个人信息")
      |> assign(:user, user_with_asset)
      |> assign(:superior_info, superior_info)
      |> assign(:refund_transactions, refund_transactions)
      |> assign(:show_refund_modal, false)
      |> assign(:show_password_modal, false)
      |> assign(:refund_amount, "")
      |> assign(:password_form, %{
        "current_password" => "",
        "new_password" => "",
        "confirm_password" => ""
      })

    {:ok, socket}
  end

  def handle_event("show_refund_modal", _params, socket) do
    {:noreply, assign(socket, :show_refund_modal, true)}
  end

  def handle_event("hide_refund_modal", _params, socket) do
    {:noreply, assign(socket, :show_refund_modal, false)}
  end

  def handle_event("show_password_modal", _params, socket) do
    {:noreply, assign(socket, :show_password_modal, true)}
  end

  def handle_event("hide_password_modal", _params, socket) do
    {:noreply, assign(socket, :show_password_modal, false)}
  end

  def handle_event("update_refund_amount", %{"amount" => amount}, socket) do
    {:noreply, assign(socket, :refund_amount, amount)}
  end

  def handle_event("submit_refund", _params, socket) do
    current_user = socket.assigns.current_user
    amount = socket.assigns.refund_amount

    case parse_amount(amount) do
      {:ok, parsed_amount} when parsed_amount > 0 ->
        case process_refund(current_user, parsed_amount) do
          {:ok, _transaction} ->
            socket =
              socket
              |> put_flash(:info, "退费申请已提交，等待上线确认")
              |> assign(:show_refund_modal, false)
              |> assign(:refund_amount, "")
              |> update_refund_transactions()

            {:noreply, socket}

          {:error, reason} ->
            {:noreply, put_flash(socket, :error, "退费失败：#{reason}")}
        end

      {:ok, _} ->
        {:noreply, put_flash(socket, :error, "退费金额必须大于0")}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "请输入有效的金额")}
    end
  end

  def handle_event("update_password_form", %{"field" => field, "value" => value}, socket) do
    password_form = Map.put(socket.assigns.password_form, field, value)
    {:noreply, assign(socket, :password_form, password_form)}
  end

  def handle_event("submit_password_change", _params, socket) do
    current_user = socket.assigns.current_user
    form = socket.assigns.password_form

    case change_password(current_user, form) do
      {:ok, _user} ->
        socket =
          socket
          |> put_flash(:info, "密码修改成功")
          |> assign(:show_password_modal, false)
          |> assign(:password_form, %{
            "current_password" => "",
            "new_password" => "",
            "confirm_password" => ""
          })

        {:noreply, socket}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "密码修改失败：#{reason}")}
    end
  end

  # 私有函数

  defp get_superior_info(user_id) do
    case AgentRelationship.get_by_subordinate(%{subordinate_id: user_id}) do
      {:ok, [relationship | _]} ->
        agent = User.get_by_id!(relationship.agent_id)
        %{
          agent: agent,
          commission_rate: relationship.commission_rate,
          level: relationship.level
        }

      _ ->
        nil
    end
  end

  defp get_refund_transactions(user_id) do
    case PointsTransaction.get_user_transactions(%{user_id: user_id, limit: 20}) do
      {:ok, transactions} ->
        Enum.filter(transactions, fn t -> t.transaction_type == :refund end)

      _ ->
        []
    end
  end

  defp update_refund_transactions(socket) do
    current_user = socket.assigns.current_user
    refund_transactions = get_refund_transactions(current_user.id)
    assign(socket, :refund_transactions, refund_transactions)
  end

  defp parse_amount(amount_str) when is_binary(amount_str) do
    case Integer.parse(amount_str) do
      {amount, ""} -> {:ok, amount}
      _ -> {:error, :invalid_format}
    end
  end

  defp process_refund(user, amount) do
    # 检查用户是否有上线
    case get_superior_info(user.id) do
      nil ->
        {:error, "您没有上线，无法申请退费"}

      superior_info ->
        # 检查用户余额
        user_with_asset = User.get_by_id!(user.id, load: [:asset])
        current_balance = user_with_asset.asset.points

        if current_balance < amount do
          {:error, "余额不足"}
        else
          # 创建退费交易记录，状态为确认中
          PointsTransaction.create_transaction(%{
            user_id: user.id,
            transaction_type: :refund,
            amount: -amount,
            balance_before: current_balance,
            balance_after: current_balance - amount,
            status: :pending_confirmation,
            description: "申请退费给上线：#{superior_info.agent.username}",
            extra_data: %{
              agent_id: superior_info.agent.id,
              agent_username: superior_info.agent.username,
              refund_amount: amount
            }
          })
        end
    end
  end

  defp change_password(user, form) do
    current_password = Map.get(form, "current_password", "")
    new_password = Map.get(form, "new_password", "")
    confirm_password = Map.get(form, "confirm_password", "")

    cond do
      String.length(current_password) == 0 ->
        {:error, "请输入当前密码"}

      String.length(new_password) < 6 ->
        {:error, "新密码长度至少6位"}

      new_password != confirm_password ->
        {:error, "两次输入的密码不一致"}

      true ->
        # 这里应该调用实际的密码修改逻辑
        # 暂时返回成功，实际实现需要验证当前密码并更新
        {:ok, user}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8 max-w-4xl">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-base-content">个人信息</h1>
        <p class="text-base-content/70 mt-2">管理您的个人信息和账户设置</p>
      </div>

      <!-- 基本信息卡片 -->
      <div class="card bg-base-100 shadow-xl mb-6">
        <div class="card-body">
          <h2 class="card-title text-xl mb-4">
            <.icon name="hero-user" class="w-6 h-6" />
            基本信息
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">用户名</span>
              </label>
              <div class="input input-bordered bg-base-200 flex items-center">
                {@user.username}
              </div>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">用户ID</span>
              </label>
              <div class="input input-bordered bg-base-200 flex items-center">
                用户{@user.numeric_id}
              </div>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">当前积分</span>
              </label>
              <div class="input input-bordered bg-base-200 flex items-center">
                <.icon name="hero-currency-dollar" class="w-5 h-5 mr-2 text-success" />
                {@user.asset.points}
              </div>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">账户类型</span>
              </label>
              <div class="input input-bordered bg-base-200 flex items-center">
                <span class={[
                  "badge",
                  if(@user.permission_level >= 1, do: "badge-error", else: "badge-info")
                ]}>
                  <%= case @user.permission_level do %>
                    <% 2 -> %> 超级管理员
                    <% 1 -> %> 管理员
                    <% _ -> %> 普通用户
                  <% end %>
                </span>
                <%= if @user.agent_level >= 0 do %>
                  <span class="badge badge-warning ml-2">
                    <%= if @user.agent_level == 0, do: "根代理", else: "下级代理 L#{@user.agent_level}" %>
                  </span>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 上线信息卡片 -->
      <%= if @superior_info do %>
        <div class="card bg-base-100 shadow-xl mb-6">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">
              <.icon name="hero-user-group" class="w-6 h-6" />
              上线信息
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">上线用户名</span>
                </label>
                <div class="input input-bordered bg-base-200 flex items-center">
                  {@superior_info.agent.username}
                </div>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">抽水比例</span>
                </label>
                <div class="input input-bordered bg-base-200 flex items-center">
                  {Decimal.mult(@superior_info.commission_rate, 100) |> Decimal.to_string()}%
                </div>
              </div>
            </div>

            <div class="card-actions justify-end mt-4">
              <button class="btn btn-warning" phx-click="show_refund_modal">
                <.icon name="hero-arrow-up-tray" class="w-5 h-5 mr-2" />
                申请退费
              </button>
            </div>
          </div>
        </div>
      <% end %>

      <!-- 账户操作卡片 -->
      <div class="card bg-base-100 shadow-xl mb-6">
        <div class="card-body">
          <h2 class="card-title text-xl mb-4">
            <.icon name="hero-cog-6-tooth" class="w-6 h-6" />
            账户操作
          </h2>
          
          <div class="card-actions">
            <button class="btn btn-primary" phx-click="show_password_modal">
              <.icon name="hero-key" class="w-5 h-5 mr-2" />
              修改密码
            </button>
          </div>
        </div>
      </div>

      <!-- 退费记录卡片 -->
      <%= if length(@refund_transactions) > 0 do %>
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">
              <.icon name="hero-clock" class="w-6 h-6" />
              退费记录
            </h2>
            
            <div class="overflow-x-auto">
              <table class="table table-zebra">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>金额</th>
                    <th>状态</th>
                    <th>描述</th>
                  </tr>
                </thead>
                <tbody>
                  <%= for transaction <- @refund_transactions do %>
                    <tr>
                      <td>{Calendar.strftime(transaction.inserted_at, "%Y-%m-%d %H:%M")}</td>
                      <td class="text-error font-medium">{abs(transaction.amount)}</td>
                      <td>
                        <span class={[
                          "badge",
                          case transaction.status do
                            :pending_confirmation -> "badge-warning"
                            :confirmed -> "badge-success"
                            :rejected -> "badge-error"
                          end
                        ]}>
                          <%= case transaction.status do %>
                            <% :pending_confirmation -> %> 确认中
                            <% :confirmed -> %> 已确认
                            <% :rejected -> %> 已拒绝
                          <% end %>
                        </span>
                      </td>
                      <td>{transaction.description}</td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- 退费申请模态框 -->
    <%= if @show_refund_modal do %>
      <div class="modal modal-open">
        <div class="modal-box">
          <h3 class="font-bold text-lg mb-4">申请退费</h3>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">退费金额</span>
            </label>
            <input 
              type="number" 
              class="input input-bordered" 
              placeholder="请输入退费金额"
              value={@refund_amount}
              phx-keyup="update_refund_amount"
              phx-value-amount={@refund_amount}
            />
            <label class="label">
              <span class="label-text-alt">当前余额：{@user.asset.points}</span>
            </label>
          </div>

          <div class="alert alert-warning mb-4">
            <.icon name="hero-exclamation-triangle" class="w-6 h-6" />
            <span>退费申请提交后，积分将立即扣除，等待上线确认后才会转给上线。</span>
          </div>

          <div class="modal-action">
            <button class="btn btn-ghost" phx-click="hide_refund_modal">取消</button>
            <button class="btn btn-warning" phx-click="submit_refund">确认申请</button>
          </div>
        </div>
      </div>
    <% end %>

    <!-- 修改密码模态框 -->
    <%= if @show_password_modal do %>
      <div class="modal modal-open">
        <div class="modal-box">
          <h3 class="font-bold text-lg mb-4">修改密码</h3>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">当前密码</span>
            </label>
            <input 
              type="password" 
              class="input input-bordered" 
              placeholder="请输入当前密码"
              value={@password_form["current_password"]}
              phx-keyup="update_password_form"
              phx-value-field="current_password"
              phx-value-value={@password_form["current_password"]}
            />
          </div>

          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">新密码</span>
            </label>
            <input 
              type="password" 
              class="input input-bordered" 
              placeholder="请输入新密码（至少6位）"
              value={@password_form["new_password"]}
              phx-keyup="update_password_form"
              phx-value-field="new_password"
              phx-value-value={@password_form["new_password"]}
            />
          </div>

          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">确认新密码</span>
            </label>
            <input 
              type="password" 
              class="input input-bordered" 
              placeholder="请再次输入新密码"
              value={@password_form["confirm_password"]}
              phx-keyup="update_password_form"
              phx-value-field="confirm_password"
              phx-value-value={@password_form["confirm_password"]}
            />
          </div>

          <div class="modal-action">
            <button class="btn btn-ghost" phx-click="hide_password_modal">取消</button>
            <button class="btn btn-primary" phx-click="submit_password_change">确认修改</button>
          </div>
        </div>
      </div>
    <% end %>
    """
  end
end
