defmodule CypridinaWeb.Live.AgentRestrictedLive do
  @moduledoc """
  代理权限控制的 LiveResource 基类

  提供代理权限控制功能：
  - 代理只能查看自己的直接下级
  - 代理只能设置抽水比例
  - 管理员可以查看所有数据
  """

  defmacro __using__(opts) do
    quote do
      use Backpex.LiveResource, unquote(opts)

      alias <PERSON>pridina.Accounts.{AdminService, AgentService}

      @impl Backpex.LiveResource
      def can?(assigns, action, item \\ nil)

      # 管理员可以执行所有操作
      def can?(%{current_user: user} = assigns, _action, _item) when not is_nil(user) do
        if AdminService.is_admin?(user) do
          true
        else
          # 代理权限检查
          agent_can?(assigns, _action, _item)
        end
      end

      # 未登录用户无权限
      def can?(_assigns, _action, _item), do: false

      # 代理权限检查（子类可以重写）
      def agent_can?(%{current_user: user} = assigns, action, item) do
        case action do
          :index -> AgentService.is_agent?(user)
          :show -> can_view_item?(user, item)
          :edit -> can_edit_item?(user, item)
          :new -> AgentService.is_agent?(user)
          # 代理不能删除
          :delete -> false
          _ -> false
        end
      end

      # 检查是否可以查看项目（子类应该重写）
      def can_view_item?(user, item), do: false

      # 检查是否可以编辑项目（子类应该重写）
      def can_edit_item?(user, item), do: false

      # 过滤查询结果，只显示代理可以看到的数据
      @impl Backpex.LiveResource
      def filters(assigns) do
        user = assigns[:current_user]

        if user && AdminService.is_admin?(user) do
          # 管理员可以看到所有数据
          []
        else
          # 代理只能看到自己的数据
          agent_filters(assigns)
        end
      end

      # 代理数据过滤（子类应该重写）
      def agent_filters(assigns), do: []

      # 获取当前用户的直接下级ID列表
      def get_subordinate_ids(user) do
        if AgentService.is_agent?(user) do
          user.id
          |> AgentService.get_direct_subordinates()
          |> Enum.map(& &1.id)
        else
          []
        end
      end

      # 检查用户是否为指定用户的代理
      def is_agent_of?(agent_user, target_user_id) do
        AgentService.can_manage_user?(agent_user, target_user_id)
      end

      defoverridable can?: 3,
                     agent_can?: 3,
                     can_view_item?: 2,
                     can_edit_item?: 2,
                     filters: 1,
                     agent_filters: 1
    end
  end
end
