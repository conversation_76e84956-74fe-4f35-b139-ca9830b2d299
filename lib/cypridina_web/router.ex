defmodule Cy<PERSON>ridinaWeb.Router do
  use CypridinaWeb, :router

  use AshAuthentication.Phoenix.Router

  import AshAuthentication.Plug.Helpers
  import Oban.Web.Router
  import Backpex.Router
  use ErrorTracker.Integrations.Plug
  use ErrorTracker.Web, :router

  # pipeline :mcp do
  #   plug AshAuthentication.Strategy.ApiKey.Plug,
  #     resource: Cypridina.Accounts.User,
  #     # Use `required?: false` to allow unauthenticated
  #     # users to connect, for example if some tools
  #     # are publicly accessible.
  #     required?: true,
  #     on_error: &__MODULE__.api_key_error/2
  # end

  # def api_key_error(conn, _opts) do
  #   conn
  #   |> Plug.Conn.put_status(401)
  #   |> Phoenix.Controller.json(%{error: "Unauthorized"})
  #   |> Plug.Conn.halt()
  # end

  pipeline :browser do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_live_flash)
    plug(:put_root_layout, html: {CypridinaWeb.Layouts, :root})
    plug(:protect_from_forgery)
    plug(:put_secure_browser_headers)
    plug(:load_from_session)
    plug(CypridinaWeb.Plugs.ReturnToPlug)
  end

  pipeline :api do
    plug(:accepts, ["json"])
    plug(:load_from_bearer)
    plug(:set_actor, :user)
    # plug AshAuthentication.Strategy.ApiKey.Plug, resource: Cypridina.Accounts.User
  end

  # 用户应用路由
  scope "/app", CypridinaWeb do
    pipe_through(:browser)

    # 需要用户登录的路由
    ash_authentication_live_session :user_required,
      on_mount: {CypridinaWeb.LiveUserAuth, :user_required} do
      live("/racing_game", RacingLive.Index, :index)
    end

    # 可选用户登录的路由
    ash_authentication_live_session :user_optional,
      on_mount: {CypridinaWeb.LiveUserAuth, :user_optional} do
      # 可选登录的页面
    end
  end

  # 公开路由，不需要登录也可以访问
  scope "/", CypridinaWeb do
    pipe_through(:browser)

    oban_dashboard("/oban")
    error_tracker_dashboard("/errors")

    # get("/", PageController, :home)
    get("/", PageController, :redirect_to_racing_game)
  end

  scope "/", CypridinaWeb do
    pipe_through(:browser)

    auth_routes(AuthController, Cypridina.Accounts.User, path: "/auth")
    sign_out_route(AuthController)

    # 登录相关路由
    sign_in_route(
      register_path: "/register",
      reset_path: "/reset",
      auth_routes_prefix: "/auth",
      on_mount: [{CypridinaWeb.LiveUserAuth, :live_no_user}],
      overrides: [
        CypridinaWeb.AuthOverrides,
        AshAuthentication.Phoenix.Overrides.Default
      ]
    )

    # 重置密码路由
    reset_route(
      auth_routes_prefix: "/auth",
      overrides: [
        CypridinaWeb.AuthOverrides,
        AshAuthentication.Phoenix.Overrides.Default
      ]
    )

    # 用户确认路由
    confirm_route(Cypridina.Accounts.User, :confirm_new_user,
      auth_routes_prefix: "/auth",
      overrides: [CypridinaWeb.AuthOverrides, AshAuthentication.Phoenix.Overrides.Default]
    )
  end

  # API scopes for racing game
  scope "/racing_game", CypridinaWeb do
    pipe_through(:api)

    # 赛马游戏API接口
    get("/getsettle", RacingGameController, :getsettle)
    get("/getinfo", RacingGameController, :getinfo)
    get("/getlatestdata", RacingGameController, :getlatestdata)
    post("/rank", RacingGameController, :rank)
  end

  scope "/TeenApi", CypridinaWeb do
    pipe_through(:api)

    # 基本信息端点
    get("/", TeenApiController, :default)
  end

  scope "/api", CypridinaWeb do
    pipe_through(:api)
  end

  # 统一的管理后台 - 管理员和代理都可以访问，通过权限控制显示内容
  scope "/backpex_admin", CypridinaWeb do
    pipe_through([:browser])

    # Backpex 管理界面
    backpex_routes()
    get("/", PageController, :redirect_to_admin_users)

    # 统一的管理后台LiveView
    ash_authentication_live_session :admin_backend,
      on_mount: [{CypridinaWeb.LiveUserAuth, :admin_required}, Backpex.InitAssigns] do

      # 用户管理 - 根据权限显示不同内容
      live_resources("/users", Live.UserLive)
      live_resources("/user_assets", Live.UserAssetLive)

      # 代理关系管理 - 管理员看全部，代理只看自己的下级
      live_resources("/agent_relationships", Live.AgentLive)

      # 佣金记录 - 管理员看全部，代理只看自己相关的
      live_resources("/commission_records", Live.CommissionRecordLive)

      # 比赛控制 - 所有有权限的用户都可以访问
      live("/race_control", Live.RaceControlLive, :index)

      # 管理后台首页 - 根据权限显示不同的仪表板
      # live("/", Live.AdminDashboardLive, :index)
    end
  end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:cypridina, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router
    import AshAdmin.Router

    scope "/dev" do
      pipe_through(:browser)

      live_dashboard("/dashboard", metrics: CypridinaWeb.Telemetry)
      forward("/mailbox", Plug.Swoosh.MailboxPreview)

      # ash管理后台
      ash_admin("/admin")
    end
  end
end
