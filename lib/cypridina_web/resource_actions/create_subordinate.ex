defmodule CypridinaWeb.ResourceActions.CreateSubordinate do
  @moduledoc """
  Resource action for creating subordinate accounts with agent relationships.
  """

  use Backpex.ResourceAction

  import Ecto.Changeset
  import Ecto.Query

  alias Cypridina.Accounts.{User, AgentService}

  @impl Backpex.ResourceAction
  def title, do: "创建下线账号"

  @impl Backpex.ResourceAction
  def label, do: "创建下线"

  @impl Backpex.ResourceAction
  def fields do
    [
      # agent_id: %{
      #   module: Backpex.Fields.BelongsTo,
      #   label: "代理",
      #   display_field: :username,
      #   live_resource: CypridinaWeb.Live.UserLive,
      #   options_query: fn query, _assigns ->
      #     from u in query,
      #       where: u.agent_level >= 0,
      #       order_by: [asc: u.username]
      #   end,
      #   type: :string
      # },
      username: %{
        module: Backpex.Fields.Text,
        label: "用户名",
        type: :string
      },
      password: %{
        module: Backpex.Fields.Text,
        label: "密码",
        type: :string
      },
      password_confirmation: %{
        module: Backpex.Fields.Text,
        label: "确认密码",
        type: :string
      },
      commission_rate: %{
        module: Backpex.Fields.Number,
        label: "抽水比例 (%)",
        type: :decimal
      }
    ]
  end

  @required_fields ~w[username password password_confirmation commission_rate]a

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> cast(attrs, @required_fields)
    |> validate_required(@required_fields)
    |> validate_length(:username, min: 3, max: 20)
    |> validate_length(:password, min: 6)
    |> validate_confirmation(:password)
    |> validate_number(:commission_rate,
      greater_than_or_equal_to: 0,
      less_than_or_equal_to: 100
    )
    |> validate_username_unique()
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    agent_id = socket.assigns.current_user.id
    case create_subordinate_account(agent_id, data) do
      {:ok, %{user: user, relationship: _relationship}} ->
        socket =
          socket
          |> Phoenix.LiveView.clear_flash()
          |> Phoenix.LiveView.put_flash(:info, "成功创建下线账号: #{user.username}")

        {:ok, socket}

      {:error, reason} ->
        error_message = format_error_message(reason)

        socket =
          socket
          |> Phoenix.LiveView.clear_flash()
          |> Phoenix.LiveView.put_flash(:error, "创建下线账号失败: #{error_message}")

        {:error, socket}
    end
  end

  # 创建下线账号的私有函数
  defp create_subordinate_account(agent_id, data) do
    user_params = %{
      username: data.username,
      password: data.password,
      password_confirmation: data.password_confirmation,
      commission_rate: data.commission_rate
    }

    AgentService.create_subordinate_account(agent_id, user_params)
  end

  # 验证用户名唯一性
  defp validate_username_unique(changeset) do
    username = get_field(changeset, :username)

    if username do
      # 使用 Ecto 查询而不是 Ash 查询
      case Cypridina.Ecto.Accounts.EUser
           |> Cypridina.Repo.get_by(username: username) do
        nil -> changeset
        _user -> add_error(changeset, :username, "用户名已存在")
      end
    else
      changeset
    end
  end

  # 格式化错误消息
  defp format_error_message(%Ash.Error.Invalid{errors: errors}) do
    errors
    |> Enum.map(&format_single_error/1)
    |> Enum.join(", ")
  end

  defp format_error_message(error) when is_binary(error), do: error
  defp format_error_message(_error), do: "未知错误"

  defp format_single_error(%{message: message}), do: message
  defp format_single_error(error), do: inspect(error)
end
