defmodule CypridinaWeb.LiveUserAuth do
  @moduledoc """
  统一的LiveView身份验证助手模块
  """

  import Phoenix.Component
  import Phoenix.LiveView
  use CypridinaWeb, :verified_routes
  require Logger

  alias CypridinaWeb.AuthHelper

  # 获取当前用户（用于嵌套LiveView）
  def on_mount(:current_user, params, session, socket) do
    Logger.debug("🔍 [AUTH] current_user mount - params: #{inspect(params)}")
    Logger.debug("🔍 [AUTH] current_user mount - session keys: #{inspect(Map.keys(session))}")

    result = AshAuthentication.Phoenix.LiveSession.assign_new_resources(socket, session)
    Logger.debug("🔍 [AUTH] current_user mount - assigned resources")

    {:cont, result}
  end

  # 可选用户认证 - 用户可以登录也可以不登录
  def on_mount(:user_optional, params, session, socket) do
    Logger.debug("🔍 [AUTH] user_optional mount started")
    Logger.debug("🔍 [AUTH] user_optional - params: #{inspect(params)}")
    Logger.debug("🔍 [AUTH] user_optional - session keys: #{inspect(Map.keys(session))}")

    socket = AshAuthentication.Phoenix.LiveSession.assign_new_resources(socket, session)
    Logger.debug("🔍 [AUTH] user_optional - current_user from assigns: #{inspect(socket.assigns[:current_user])}")

    case AuthHelper.get_current_user(socket.assigns[:current_user]) do
      {:ok, user} ->
        user_role = AuthHelper.get_user_role(user)
        Logger.debug("✅ [AUTH] user_optional - authenticated user: #{inspect(user.id)}, role: #{inspect(user_role)}")

        socket =
          socket
          |> assign(:current_user, user)
          |> assign(:user_role, user_role)

        {:cont, socket}

      {:error, :not_authenticated} ->
        Logger.debug("❌ [AUTH] user_optional - not authenticated, setting guest")

        socket =
          socket
          |> assign(:current_user, nil)
          |> assign(:user_role, :guest)

        {:cont, socket}
    end
  end

  # 必须用户认证 - 用户必须登录
  def on_mount(:user_required, params, session, socket) do
    Logger.debug("🔍 [AUTH] user_required mount started")
    Logger.debug("🔍 [AUTH] user_required - params: #{inspect(params)}")
    Logger.debug("🔍 [AUTH] user_required - session keys: #{inspect(Map.keys(session))}")

    socket = AshAuthentication.Phoenix.LiveSession.assign_new_resources(socket, session)
    Logger.debug("🔍 [AUTH] user_required - current_user from assigns: #{inspect(socket.assigns[:current_user])}")

    case AuthHelper.get_current_user(socket.assigns[:current_user]) do
      {:ok, user} ->
        user_role = AuthHelper.get_user_role(user)
        Logger.debug("✅ [AUTH] user_required - authenticated user: #{inspect(user.id)}, role: #{inspect(user_role)}")

        socket =
          socket
          |> assign(:current_user, user)
          |> assign(:user_role, user_role)

        {:cont, socket}

      {:error, :not_authenticated} ->
        current_path = get_current_path(socket, params)

        Logger.info("🔑 [AUTH] User not authenticated, redirecting to sign-in with redirect=#{current_path}")
        Logger.debug("❌ [AUTH] user_required - authentication failed, redirecting")

        {:halt, push_navigate(socket, to: ~p"/sign-in?return_to=#{current_path}")}
    end
  end

  # 禁止已登录用户访问 - 用于登录页面等
  def on_mount(:no_user, params, session, socket) do
    Logger.debug("🔍 [AUTH] no_user mount started")
    Logger.debug("🔍 [AUTH] no_user - params: #{inspect(params)}")

    socket = AshAuthentication.Phoenix.LiveSession.assign_new_resources(socket, session)
    Logger.debug("🔍 [AUTH] no_user - current_user from assigns: #{inspect(socket.assigns[:current_user])}")

    case AuthHelper.get_current_user(socket.assigns[:current_user]) do
      {:ok, user} ->
        Logger.debug("🚫 [AUTH] no_user - user #{inspect(user.id)} is logged in, redirecting to home")
        {:halt, push_navigate(socket, to: ~p"/")}

      {:error, :not_authenticated} ->
        Logger.debug("✅ [AUTH] no_user - no user logged in, proceeding")
        socket = assign(socket, :current_user, nil)
        {:cont, socket}
    end
  end

  # AshAuthentication 内部使用的 live_no_user - 与 no_user 相同的逻辑
  def on_mount(:live_no_user, params, session, socket) do
    Logger.debug("🔍 [AUTH] live_no_user mount started (AshAuthentication internal)")
    Logger.debug("🔍 [AUTH] live_no_user - params: #{inspect(params)}")

    socket = AshAuthentication.Phoenix.LiveSession.assign_new_resources(socket, session)
    Logger.debug("🔍 [AUTH] live_no_user - current_user from assigns: #{inspect(socket.assigns[:current_user])}")

    case AuthHelper.get_current_user(socket.assigns[:current_user]) do
      {:ok, user} ->
        Logger.debug("🚫 [AUTH] live_no_user - user #{inspect(user.id)} is logged in, redirecting to home")
        {:halt, push_navigate(socket, to: ~p"/")}

      {:error, :not_authenticated} ->
        Logger.debug("✅ [AUTH] live_no_user - no user logged in, proceeding")
        socket = assign(socket, :current_user, nil)
        {:cont, socket}
    end
  end

  # 管理后台权限检查 - 管理员和代理都可以访问，但权限不同
  def on_mount(:admin_required, params, session, socket) do
    Logger.debug("🔍 [AUTH] admin_required mount started")
    Logger.debug("🔍 [AUTH] admin_required - params: #{inspect(params)}")
    Logger.debug("🔍 [AUTH] admin_required - session keys: #{inspect(Map.keys(session))}")

    socket = AshAuthentication.Phoenix.LiveSession.assign_new_resources(socket, session)
    Logger.debug("🔍 [AUTH] admin_required - current_user from assigns: #{inspect(socket.assigns[:current_user])}")

    case AuthHelper.check_admin_or_agent_access(socket.assigns[:current_user]) do
      {:ok, user, role} ->
        Logger.info("👑 [AUTH] User #{inspect(user.id)} accessed admin area with role #{inspect(role)}")
        Logger.debug("✅ [AUTH] admin_required - access granted, user: #{inspect(user.id)}, role: #{inspect(role)}")

        permission_level_name = AuthHelper.get_permission_level_name(user)
        can_manage_users = AuthHelper.has_permission?(user, :admin)
        can_manage_agents = AuthHelper.has_permission?(user, :super_admin)
        can_view_all_data = AuthHelper.has_permission?(user, :admin)

        Logger.debug("🔧 [AUTH] admin_required - permissions: manage_users=#{can_manage_users}, manage_agents=#{can_manage_agents}, view_all_data=#{can_view_all_data}")

        socket =
          socket
          |> assign(:current_user, user)
          |> assign(:user_role, role)
          |> assign(:permission_level_name, permission_level_name)
          |> assign(:can_manage_users, can_manage_users)
          |> assign(:can_manage_agents, can_manage_agents)
          |> assign(:can_view_all_data, can_view_all_data)

        {:cont, socket}

      {:error, :not_authenticated} ->
        current_path = get_current_path(socket, params)

        Logger.info("🔑 [AUTH] User not authenticated, redirecting to sign-in with redirect=#{current_path}")
        Logger.debug("❌ [AUTH] admin_required - not authenticated, redirecting")

        {:halt, push_navigate(socket, to: ~p"/sign-in?return_to=#{current_path}")}

      {:error, :insufficient_permissions} ->
        Logger.warning("⚠️ [AUTH] User #{inspect(socket.assigns[:current_user])} attempted to access admin area without permissions")
        Logger.debug("🚫 [AUTH] admin_required - insufficient permissions, redirecting to home")

        {:halt, push_navigate(socket, to: ~p"/")}
    end
  end

  # 获取当前路径的辅助函数
  defp get_current_path(socket, params) do
    Logger.debug("🔍 [AUTH] get_current_path - params: #{inspect(params)}")

    current_uri = Phoenix.LiveView.get_connect_info(socket, :uri)
    Logger.debug("🔍 [AUTH] get_current_path - connect_info uri: #{inspect(current_uri)}")

    current_path =
      case current_uri do
        %URI{path: path, query: query} ->
          if query, do: "#{path}?#{query}", else: path

        path when is_binary(path) ->
          path

        _ ->
          "/"
      end

    final_path = if map_size(params) > 0 do
      current_path <> encode_query_string(params)
    else
      current_path
    end

    Logger.debug("🔍 [AUTH] get_current_path - final path: #{final_path}")
    final_path
  end

  # 将查询参数编码为字符串
  defp encode_query_string(params) when map_size(params) == 0, do: ""

  defp encode_query_string(params) do
    query_string =
      params
      |> Enum.map(fn {k, v} -> "#{k}=#{v}" end)
      |> Enum.join("&")

    "?" <> query_string
  end
end
