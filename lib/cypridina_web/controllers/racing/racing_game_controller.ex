defmodule CypridinaWeb.RacingGameController do
  use <PERSON><PERSON><PERSON>ina<PERSON>eb, :controller

  alias <PERSON>pridina.RacingGame.GameManager
  require Logger

  def getsettle(conn, _params) do
    result = GameManager.get_current_race_result()

    response = %{
      api: "getsettle",
      data: result,
      ret: ["SUCCESS::接口调用成功"],
      v: ""
    }

    json(conn, response)
  end

  def getinfo(conn, _params) do
    info = GameManager.get_current_game_info()

    response = %{
      api: "getinfo",
      data: info,
      ret: ["SUCCESS::接口调用成功"],
      v: ""
    }

    json(conn, response)
  end

  def getlatestdata(conn, _params) do
    data = GameManager.get_latest_race_data()

    response = %{
      api: "getlatestdata",
      data: data,
      ret: ["SUCCESS::接口调用成功"],
      v: ""
    }

    json(conn, response)
  end

  def rank(conn, params) do
    Logger.info("rank #{inspect(params)}")

    status = GameManager.get_current_race_status()
    json(conn, status)
  end
end
