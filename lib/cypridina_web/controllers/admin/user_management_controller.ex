defmodule CypridinaWeb.Admin.UserManagementController do
  use <PERSON><PERSON><PERSON>ina<PERSON>eb, :controller

  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.UserManagementService
  alias Cypridina.Accounts.User

  @doc """
  显示用户管理页面
  """
  def index(conn, _params) do
    current_user = conn.assigns[:current_user]

    with :ok <- validate_admin_permission(current_user) do
      # 获取所有用户列表
      {:ok, users} = User |> Ash.read()

      render(conn, :index, users: users, current_user: current_user)
    else
      {:error, message} ->
        conn
        |> put_flash(:error, message)
        |> redirect(to: "/")
    end
  end

  @doc """
  显示创建用户表单
  """
  def new(conn, _params) do
    current_user = conn.assigns[:current_user]

    with :ok <- validate_admin_permission(current_user) do
      render(conn, :new, current_user: current_user)
    else
      {:error, message} ->
        conn
        |> put_flash(:error, message)
        |> redirect(to: "/admin/users")
    end
  end

  @doc """
  创建新用户
  """
  def create(conn, %{"user" => user_params}) do
    current_user = conn.assigns[:current_user]

    with :ok <- validate_admin_permission(current_user),
         {:ok, user} <- create_user_by_role(current_user, user_params) do
      conn
      |> put_flash(:info, "用户 #{user.username} 创建成功")
      |> redirect(to: "/admin/users")
    else
      {:error, message} when is_binary(message) ->
        conn
        |> put_flash(:error, message)
        |> render(:new, current_user: current_user)

      {:error, changeset} ->
        conn
        |> put_flash(:error, "创建用户失败")
        |> render(:new, current_user: current_user, changeset: changeset)
    end
  end

  @doc """
  显示用户详情
  """
  def show(conn, %{"id" => user_id}) do
    current_user = conn.assigns[:current_user]

    with :ok <- validate_admin_permission(current_user),
         {:ok, user} <- User |> Ash.get(user_id) do
      render(conn, :show, user: user, current_user: current_user)
    else
      {:error, _} ->
        conn
        |> put_flash(:error, "用户不存在")
        |> redirect(to: "/admin/users")
    end
  end

  @doc """
  显示编辑用户表单
  """
  def edit(conn, %{"id" => user_id}) do
    current_user = conn.assigns[:current_user]

    with :ok <- validate_admin_permission(current_user),
         {:ok, user} <- User |> Ash.get(user_id),
         :ok <- validate_edit_permission(current_user, user) do
      render(conn, :edit, user: user, current_user: current_user)
    else
      {:error, message} when is_binary(message) ->
        conn
        |> put_flash(:error, message)
        |> redirect(to: "/admin/users")

      {:error, _} ->
        conn
        |> put_flash(:error, "用户不存在")
        |> redirect(to: "/admin/users")
    end
  end

  @doc """
  更新用户信息
  """
  def update(conn, %{"id" => user_id, "user" => user_params}) do
    current_user = conn.assigns[:current_user]

    with :ok <- validate_admin_permission(current_user),
         {:ok, user} <- User |> Ash.get(user_id),
         :ok <- validate_edit_permission(current_user, user),
         {:ok, updated_user} <- update_user(user, user_params) do
      conn
      |> put_flash(:info, "用户 #{updated_user.username} 更新成功")
      |> redirect(to: "/admin/users/#{updated_user.id}")
    else
      {:error, message} when is_binary(message) ->
        conn
        |> put_flash(:error, message)
        |> redirect(to: "/admin/users")

      {:error, changeset} ->
        {:ok, user} = User |> Ash.get(user_id)

        conn
        |> put_flash(:error, "更新用户失败")
        |> render(:edit, user: user, current_user: current_user, changeset: changeset)
    end
  end

  @doc """
  删除用户
  """
  def delete(conn, %{"id" => user_id}) do
    current_user = conn.assigns[:current_user]

    with :ok <- validate_super_admin_permission(current_user),
         {:ok, user} <- User |> Ash.get(user_id),
         :ok <- validate_delete_permission(current_user, user),
         :ok <- User |> Ash.destroy(user) do
      conn
      |> put_flash(:info, "用户 #{user.username} 删除成功")
      |> redirect(to: "/admin/users")
    else
      {:error, message} when is_binary(message) ->
        conn
        |> put_flash(:error, message)
        |> redirect(to: "/admin/users")

      {:error, _} ->
        conn
        |> put_flash(:error, "删除用户失败")
        |> redirect(to: "/admin/users")
    end
  end

  # 私有函数

  defp validate_admin_permission(user) do
    if user && user.permission_level >= 1 do
      :ok
    else
      {:error, "需要管理员权限"}
    end
  end

  defp validate_super_admin_permission(user) do
    if user && user.permission_level == 2 do
      :ok
    else
      {:error, "需要超级管理员权限"}
    end
  end

  defp validate_edit_permission(current_user, target_user) do
    cond do
      current_user.permission_level == 2 ->
        # 超级管理员可以编辑任何用户
        :ok

      current_user.permission_level == 1 && target_user.permission_level == 0 ->
        # 普通管理员只能编辑普通用户
        :ok

      current_user.id == target_user.id ->
        # 用户可以编辑自己
        :ok

      true ->
        {:error, "没有权限编辑此用户"}
    end
  end

  defp validate_delete_permission(current_user, target_user) do
    cond do
      current_user.id == target_user.id ->
        {:error, "不能删除自己"}

      target_user.permission_level >= current_user.permission_level ->
        {:error, "不能删除同级或更高级别的用户"}

      true ->
        :ok
    end
  end

  defp create_user_by_role(current_user, user_params) do
    case current_user.permission_level do
      2 ->
        # 超级管理员可以创建管理员
        if Map.get(user_params, "permission_level", "0") == "1" do
          UserManagementService.create_admin_by_super_admin(current_user.id, user_params)
        else
          UserManagementService.create_user_by_admin(current_user.id, user_params)
        end

      1 ->
        # 普通管理员只能创建普通用户
        UserManagementService.create_user_by_admin(current_user.id, user_params)

      _ ->
        {:error, "没有权限创建用户"}
    end
  end

  defp update_user(user, params) do
    # 这里可以根据需要实现用户更新逻辑
    # 暂时返回原用户
    {:ok, user}
  end
end
