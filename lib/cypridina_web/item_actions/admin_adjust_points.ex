defmodule CypridinaWeb.ItemActions.AdminAdjustPoints do
  @moduledoc """
  Item action for admin to directly adjust user points.
  Only available to super admins.
  """

  use BackpexWeb, :item_action

  require Logger
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts
  alias <PERSON><PERSON>ridina.Accounts.UserAsset

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-cog-6-tooth"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-orange-600"
    />
    """
  end

  @impl Backpex.ItemAction
  def fields do
    [
      operation: %{
        module: Backpex.Fields.Select,
        label: "操作类型",
        options: [
          {"增加积分", "add"},
          {"减少积分", "subtract"},
          {"设置积分", "set"}
        ]
      },
      amount: %{
        module: Backpex.Fields.Number,
        label: "积分数量",
        type: :integer,
        placeholder: "请输入积分数量"
      },
      reason: %{
        module: Backpex.Fields.Text,
        label: "操作原因",
        type: :string,
        placeholder: "请输入操作原因（必填）"
      }
    ]
  end

  @impl Backpex.ItemAction
  def label(_assigns, nil), do: "调整积分"
  def label(_assigns, item), do: "调整 #{item.username} 的积分"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "请填写表单完成积分调整操作。此操作将被记录到系统日志中。"

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "调整积分"

  @impl Backpex.ItemAction
  def cancel_label(_assigns), do: "取消"

  @required_fields ~w[operation amount reason]a
  @optional_fields ~w[]a

  @impl Backpex.ItemAction
  def changeset(change, attrs, _metadata) do
    change
    |> Ecto.Changeset.cast(attrs, @required_fields ++ @optional_fields)
    |> Ecto.Changeset.validate_required(@required_fields)
    |> Ecto.Changeset.validate_number(:amount, greater_than: 0)
    |> Ecto.Changeset.validate_inclusion(:operation, ["add", "subtract", "set"])
  end

  @impl Backpex.ItemAction
  def handle(socket, items, params) do
    # 获取目标用户（选中的第一个用户）
    [target_user | _] = items
    current_user = socket.assigns.current_user

    # 检查权限
    unless Accounts.is_super_admin?(current_user) do
      socket = put_flash(socket, :error, "只有超级管理员才能调整用户积分")
      {:ok, socket}
    else
      Logger.info("admin_adjust_points: #{inspect(params)} by #{current_user.username}")

      # 执行积分调整操作
      case perform_adjustment(target_user, current_user, params) do
        {:ok, result} ->
          message = format_success_message(target_user.username, params, result)
          socket = put_flash(socket, :info, message)
          {:ok, socket}

        {:error, reason} ->
          error_message = format_error_message(reason)
          Logger.error("积分调整失败: #{inspect(reason)}")

          socket = put_flash(socket, :error, "调整积分失败: #{error_message}")
          {:ok, socket}
      end
    end
  end

  # 执行积分调整
  defp perform_adjustment(target_user, admin_user, %{operation: operation, amount: amount, reason: reason}) do
    user_asset = UserAsset.get_by_user_id!(target_user.id)
    current_points = user_asset.points

    opts = [
      transaction_type: get_transaction_type(operation),
      admin_id: admin_user.id,
      admin_username: admin_user.username,
      reason: reason
    ]

    case operation do
      "add" ->
        Accounts.add_points(target_user.id, amount, opts)

      "subtract" ->
        if current_points >= amount do
          Accounts.subtract_points(target_user.id, amount, opts)
        else
          {:error, "用户积分不足，当前积分: #{current_points}"}
        end

      "set" ->
        # 设置积分 = 先计算差值，然后增加或减少
        difference = amount - current_points

        cond do
          difference > 0 ->
          # 需要增加积分
          Accounts.add_points(target_user.id, difference, opts)
         difference < 0 ->
          # 需要减少积分
          Accounts.subtract_points(target_user.id, abs(difference), opts)
        true->
          # 积分相同，无需操作
          {:ok, user_asset}
        end
    end
  end

  # 获取事务类型
  defp get_transaction_type("add"), do: :admin_add
  defp get_transaction_type("subtract"), do: :admin_subtract
  defp get_transaction_type("set"), do: :admin_add  # 设置操作根据实际增减来确定

  # 格式化成功消息
    defp format_success_message(username, %{operation: operation, amount: amount}, _result) do
      case operation do
        "add" -> "成功为 #{username} 增加 #{amount} 积分"
        "subtract" -> "成功为 #{username} 减少 #{amount} 积分"
          "set" -> "成功将 #{username} 的积分设置为 #{amount}"
        end
    end

  # 格式化错误消息
  defp format_error_message(error) when is_binary(error), do: error

  defp format_error_message(%Ecto.Changeset{errors: errors}) do
    errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end

  defp format_error_message(_error), do: "未知错误"
end
