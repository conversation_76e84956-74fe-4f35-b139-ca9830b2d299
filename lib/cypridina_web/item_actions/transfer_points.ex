defmodule CypridinaWeb.ItemActions.TransferPoints do
  @moduledoc """
  Item action for transferring points from current user to selected user.
  """

  use BackpexWeb, :item_action

  require Logger
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-arrow-right-circle"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end

  @impl Backpex.ItemAction
  def fields do
    [
      amount: %{
        module: Backpex.Fields.Number,
        label: "转移数量",
        type: :integer,
        placeholder: "请输入转移积分数量"
      },
      reason: %{
        module: Backpex.Fields.Text,
        label: "转移原因",
        type: :string,
        placeholder: "请输入转移原因（可选）"
      }
    ]
  end

  @impl Backpex.ItemAction
  def label(_assigns, nil), do: "转移积分"
  def label(_assigns, item), do: "向 #{item.username} 转移积分"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "请填写表单完成积分转移操作。"

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "转移积分"

  @impl Backpex.ItemAction
  def cancel_label(_assigns), do: "取消"

  @required_fields ~w[amount]a
  @optional_fields ~w[reason]a

  # @impl Backpex.ItemAction
  def changeset(change, attrs, metadata) do
    change
    |> Ecto.Changeset.cast(attrs, @required_fields ++ @optional_fields)
  end

  # @impl Backpex.ItemAction
  # def base_schema(assigns) do
  #   [item | _other] = assigns.selected_items
  #   item
  # end

  @impl Backpex.ItemAction
  def handle(socket, items, params) do
    # 获取目标用户（选中的第一个用户）
    [target_user | _] = items
    current_user = socket.assigns.current_user

    Logger.info("transfer_points: #{inspect(params)}")

    # 执行转移操作
    case Accounts.transfer_points(
           current_user.id,
           target_user.id,
           params.amount,
           Map.get(params, :reason, "")
         ) do
      {:ok, _result} ->
        socket = put_flash(socket, :info, "成功向 #{target_user.username} 转移 #{params.amount} 积分")
        {:ok, socket}

      {:error, reason} ->
        error_message = format_error_message(reason)
          Logger.info("积分转移失败: #{inspect(reason)}")

        socket = put_flash(socket, :error, "转移积分失败: #{error_message}")
        {:ok, socket}
    end
  end

  # 格式化错误消息
  defp format_error_message(error) when is_binary(error), do: error

  defp format_error_message(%Ecto.Changeset{errors: errors}) do
    errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end

  defp format_error_message(_error), do: "未知错误"
end
