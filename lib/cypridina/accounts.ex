defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts do
  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [
      AshAdmin.Domain
      # AshAuthentication
    ]

  require Logger

  admin do
    show? true
  end

  resources do
    resource Cypridina.Accounts.Token
    resource Cypridina.Accounts.User
    resource Cypridina.Accounts.UserIdentity
    resource Cypridina.Accounts.UserAsset
    resource Cypridina.Accounts.AgentRelationship
    resource Cypridina.Accounts.CommissionRecord
    resource Cypridina.Accounts.ApiKey
  end

  alias Cypridina.Accounts.{UserAsset, User}
  alias <PERSON>pridina.RacingGame.PointsTransactionHelper

  def get_user_points_by_numberic_id(numberic_id) do
    user = User.get_by_numeric_id!(numberic_id)
    user_asset = UserAsset.get_by_user_id!(user.id)
    user_asset.points
  end

  def get_user_points(user_id) do
    user_asset = UserAsset.get_by_user_id!(user_id)
    user_asset.points
  end

  def add_points(user_id, amount) when amount > 0 do
    add_points(user_id, amount, [])
  end

  def add_points(user_id, amount, opts) when amount > 0 do
    user_asset = UserAsset.get_by_user_id!(user_id)
    balance_before = user_asset.points

    case UserAsset.add_points(user_asset, %{amount: amount}) do
      {:ok, updated_asset} ->
        balance_after = updated_asset.points

        # 记录积分变动
        record_points_transaction(user_id, amount, balance_before, balance_after, :add, opts)

        {:ok, updated_asset}

      error ->
        error
    end
  end

  def subtract_points(user_id, amount) when amount > 0 do
    subtract_points(user_id, amount, [])
  end

  def subtract_points(user_id, amount, opts) when amount > 0 do
    user_asset = UserAsset.get_by_user_id!(user_id)
    balance_before = user_asset.points

    if user_asset.points >= amount do
      case UserAsset.subtract_points(user_asset, %{amount: amount}) do
        {:ok, updated_asset} ->
          balance_after = updated_asset.points

          # 记录积分变动（负数表示支出）
          record_points_transaction(
            user_id,
            -amount,
            balance_before,
            balance_after,
            :subtract,
            opts
          )

          {:ok, updated_asset}

        error ->
          error
      end
    else
      {:error, "积分不足"}
    end
  end

  @doc """
  转移积分从一个用户到另一个用户

  ## 参数
  - from_user_id: 转出用户ID
  - to_user_id: 转入用户ID
  - amount: 转移数量
  - reason: 转移原因（可选）

  ## 返回值
  - {:ok, result} 转移成功
  - {:error, reason} 转移失败

  ## 示例
      iex> Cypridina.Accounts.transfer_points(1, 2, 100, "奖励")
      {:ok, %{from_asset: %UserAsset{}, to_asset: %UserAsset{}}}

      iex> Cypridina.Accounts.transfer_points(1, 2, 1000000, "测试")
      {:error, "积分不足"}
  """

  def transfer_points(from_user_id, to_user_id, amount, reason) when from_user_id == to_user_id do
    {:error, "不能向自己转移积分"}
  end

  def transfer_points(from_user_id, to_user_id, amount, reason) when amount <= 0 do
    {:error, "积分设置错误"}
  end

  def transfer_points(from_user_id, to_user_id, amount, reason) do
    Cypridina.Repo.transaction(fn ->
      # 获取用户信息用于记录
      from_user = get_user_by_id(from_user_id)
      to_user = get_user_by_id(to_user_id)

      from_username =
        case from_user do
          {:ok, user} -> user.username
          _ -> "未知用户"
        end

      to_username =
        case to_user do
          {:ok, user} -> user.username
          _ -> "未知用户"
        end

      # 构建选项用于记录
      from_opts = [
        transaction_type: :transfer_out,
        to_user_id: to_user_id,
        to_username: to_username,
        reason: reason
      ]

      to_opts = [
        transaction_type: :transfer_in,
        from_user_id: from_user_id,
        from_username: from_username,
        reason: reason
      ]

      with {:ok, updated_from} <- subtract_points(from_user_id, amount, from_opts),
           {:ok, updated_to} <- add_points(to_user_id, amount, to_opts),
           :ok <- log_transfer(from_user_id, to_user_id, amount, reason) do
        %{
          from_asset: updated_from,
          to_asset: updated_to,
          amount: amount,
          reason: reason
        }
      else
        {:error, error_reason} ->
          Cypridina.Repo.rollback(error_reason)
      end
    end)
  end

  defp log_transfer(from_user_id, to_user_id, amount, reason) do
    # 记录转移日志（可选实现）
    require Logger
    Logger.info("积分转移: 从用户#{from_user_id}向用户#{to_user_id}转移#{amount}积分，原因: #{reason || "无"}")
    :ok
  end

  # 记录积分变动的辅助函数
  defp record_points_transaction(
         user_id,
         amount,
         balance_before,
         balance_after,
         operation_type,
         opts
       ) do
    transaction_type = Keyword.get(opts, :transaction_type)

    case {operation_type, transaction_type} do
      {:add, :transfer_in} ->
        PointsTransactionHelper.record_transfer_in(
          user_id,
          amount,
          balance_before,
          balance_after,
          opts
        )

      {:subtract, :transfer_out} ->
        PointsTransactionHelper.record_transfer_out(
          user_id,
          amount,
          balance_before,
          balance_after,
          opts
        )

      {:add, :admin_add} ->
        PointsTransactionHelper.record_admin_add(
          user_id,
          amount,
          balance_before,
          balance_after,
          opts
        )

      {:subtract, :admin_subtract} ->
        PointsTransactionHelper.record_admin_subtract(
          user_id,
          amount,
          balance_before,
          balance_after,
          opts
        )

      {:add, _} ->
        PointsTransactionHelper.record_manual_add(
          user_id,
          amount,
          balance_before,
          balance_after,
          opts
        )

      {:subtract, _} ->
        PointsTransactionHelper.record_manual_subtract(
          user_id,
          amount,
          balance_before,
          balance_after,
          opts
        )
    end
  end

  # 获取用户信息的辅助函数
  defp get_user_by_id(user_id) do
    User |> Ash.get(user_id)
  end

  # 检查是否为超级管理员
  def is_super_admin?(%{permission_level: level}) when level >= 2, do: true
  def is_super_admin?(_), do: false

  # 管理员服务相关函数
  alias Cypridina.Accounts
  alias Cypridina.Accounts.AdminService

  defdelegate is_admin?(user), to: AdminService
  defdelegate is_super_admin?(user), to: AdminService
  defdelegate has_permission_level?(user, required_level), to: AdminService
  defdelegate has_permission?(user, permission), to: AdminService
  defdelegate create_admin(attrs), to: AdminService
  defdelegate update_permission_level(user, permission_level), to: AdminService
  defdelegate list_admins(), to: AdminService
  defdelegate list_super_admins(), to: AdminService
  defdelegate create_super_admin(attrs), to: AdminService
  defdelegate super_admin_exists?(), to: AdminService
  defdelegate require_permission_level(user, required_level, fun), to: AdminService
  defdelegate require_permission_level(user, required_level, fun, args), to: AdminService
  defdelegate require_permission(user, permission, fun), to: AdminService
  defdelegate require_permission(user, permission, fun, args), to: AdminService
  defdelegate permission_level_name(level), to: AdminService
  defdelegate permission_levels(), to: AdminService
  defdelegate user_level(), to: AdminService
  defdelegate admin_level(), to: AdminService
  defdelegate super_admin_level(), to: AdminService

  defdelegate log_admin_action(admin_user, action, target \\ nil, details \\ %{}),
    to: AdminService

  # 兼容性函数
  defdelegate is_root_admin?(user), to: AdminService
  defdelegate create_root_admin(attrs), to: AdminService
  defdelegate root_admin_exists?(), to: AdminService
end
