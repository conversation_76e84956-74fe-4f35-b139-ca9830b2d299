# lib/cypridina/racing_game.ex
# Every 3 minutes
defmodule Cyprid<PERSON>.RacingGameScheduler do
  use Oban.Worker, queue: :default, max_attempts: 1
  require Logger

  @version "1.0.1"

  @impl true
  def perform(_job) do
    Logger.info("RacingGameScheduler version #{@version} running")

    # 业务逻辑
    Cypridina.RacingGame.generate_race()
    :ok
  end
end

defmodule Cypridina.RacingGame do
  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  admin do
    show? true
  end

  resources do
    resource Cypridina.RacingGame.Racer
    resource Cypridina.RacingGame.Leaderboard
    resource Cypridina.RacingGame.Race
    resource Cypridina.RacingGame.Bet
    resource Cypridina.RacingGame.Stock
    resource Cypridina.RacingGame.StockStatistics
    resource Cypridina.RacingGame.UserStockStatistics
    resource Cypridina.RacingGame.StockStatistics
    resource Cypridina.RacingGame.PointsTransaction
  end

  # 股票玩法
  # 添加用户资产相关的辅助函数
  def get_user_stocks(user_id) do
    # 使用get_by_user_id而不是直接用Ash.get!
    user_stocks = get_or_create_user_stocks(user_id)
    user_stocks
  end

  # def buy_stocks(user_id, amount) when amount > 0 do
  #   user_stocks = get_or_create_user_stocks(user_id)

  #   Cypridina.Accounts.UserAsset.add_points(user_asset, %{
  #     # points: user_asset.points,
  #     amount: amount
  #   })
  # end

  # def sell_stocks(user_id, amount) when amount > 0 do
  #   user_asset = get_or_create_user_stocks(user_id)

  #   if user_asset.points >= amount do
  #     Cypridina.Accounts.UserAsset.subtract_points(user_asset, %{
  #       # points: user_asset.points,
  #       amount: amount
  #     })
  #   else
  #     {:error, "积分不足"}
  #   end
  # end

  defp get_or_create_user_stocks(user_id) do
    case Cypridina.RacingGame.Stock.get_user_stocks(user_id) do
      {:error, _reason} ->
        {:ok, stock} = Cypridina.RacingGame.Stock.create(%{user_id: user_id, points: 0})
        stock

      {:ok, stock} ->
        stock
    end
  end
end
