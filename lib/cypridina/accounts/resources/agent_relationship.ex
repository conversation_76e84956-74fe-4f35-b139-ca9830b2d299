defmodule Cypridina.Accounts.AgentRelationship do
  @moduledoc """
  代理关系模型 - 存储代理和下线的关系
  """
  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.Accounts,
    authorizers: [Ash.Policy.Authorizer],
    data_layer: AshPostgres.DataLayer

  postgres do
    table "agent_relationships"
    repo Cypridina.Repo
  end

  code_interface do
    define :create_relationship, action: :create
    define :get_by_agent, action: :get_by_agent
    define :get_by_subordinate, action: :get_by_subordinate
    define :get_agent_tree, action: :get_agent_tree
    define :check_relationship, action: :check_relationship
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept [:agent_id, :subordinate_id, :level, :commission_rate]

      # argument :agent_id, :uuid, allow_nil?: false
      # argument :subordinate_id, :uuid, allow_nil?: false
      # argument :level, :integer, default: 1
      # argument :commission_rate, :decimal, default: Decimal.new("0.05")

      # change set_attribute(:agent_id, arg(:agent_id))
      # change set_attribute(:subordinate_id, arg(:subordinate_id))
      # change set_attribute(:level, arg(:level))
      # change set_attribute(:commission_rate, arg(:commission_rate))

      # 验证不能自己做自己的代理
      validate fn changeset, _context ->
        agent_id = Ash.Changeset.get_attribute(changeset, :agent_id)
        subordinate_id = Ash.Changeset.get_attribute(changeset, :subordinate_id)

        if agent_id == subordinate_id do
          {:error, field: :subordinate_id, message: "不能将自己设为下线"}
        else
          :ok
        end
      end
    end

    read :get_by_agent do
      argument :agent_id, :uuid, allow_nil?: false
      filter expr(agent_id == ^arg(:agent_id))
    end

    read :get_by_subordinate do
      argument :subordinate_id, :uuid, allow_nil?: false
      filter expr(subordinate_id == ^arg(:subordinate_id))
    end

    read :get_agent_tree do
      argument :agent_id, :uuid, allow_nil?: false
      # 获取代理的所有下线（包括多级）
      filter expr(agent_id == ^arg(:agent_id))
      prepare build(sort: [level: :asc, inserted_at: :asc])
    end

    read :check_relationship do
      argument :agent_id, :uuid, allow_nil?: false
      argument :subordinate_id, :uuid, allow_nil?: false
      filter expr(agent_id == ^arg(:agent_id) and subordinate_id == ^arg(:subordinate_id))
    end
  end

  policies do
    # 暂时允许所有操作，简化权限
    policy always() do
      authorize_if always()
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :agent_id, :uuid do
      allow_nil? false
      public? true
      description "代理用户ID"
    end

    attribute :subordinate_id, :uuid do
      allow_nil? false
      public? true
      description "下线用户ID"
    end

    attribute :level, :integer do
      allow_nil? false
      default 1
      public? true
      description "代理层级，1为直接下线"
    end

    attribute :commission_rate, :decimal do
      allow_nil? false
      default Decimal.new("0.05")
      public? true
      description "抽水比例，默认5%"

      constraints min: Decimal.new("0"),
                  max: Decimal.new("1")
    end

    attribute :status, :integer do
      allow_nil? false
      default 1
      public? true
      description "关系状态：1-有效，0-无效"
    end

    timestamps()
  end

  relationships do
    belongs_to :agent, Cypridina.Accounts.User do
      public? true
      source_attribute :agent_id
      destination_attribute :id
    end

    belongs_to :subordinate, Cypridina.Accounts.User do
      public? true
      source_attribute :subordinate_id
      destination_attribute :id
    end
  end

  identities do
    # 确保同一对代理-下线关系只能存在一条记录
    identity :unique_agent_subordinate, [:agent_id, :subordinate_id]
  end
end
