defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.UserAsset do
  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cy<PERSON><PERSON>ina.Accounts,
    authorizers: [Ash.Policy.Authorizer],
    data_layer: AshPostgres.DataLayer

  postgres do
    table "user_assets"
    repo Cyprid<PERSON>.Repo
  end

  code_interface do
    define :get_by_user_id, action: :read, get_by_identity: :unique_user_id
    define :add_points, action: :add_points
    define :subtract_points, action: :subtract_points
    define :add_commission, action: :add_commission
    define :subtract_commission, action: :subtract_commission
    define :create
  end

  actions do
    defaults [:read, :update, :destroy, create: :*]

    update :add_points do
      argument :amount, :integer, allow_nil?: false
      change increment(:points, amount: arg(:amount))
    end

    update :subtract_points do
      argument :amount, :integer, allow_nil?: false
      change atomic_update(:points, expr(points - ^arg(:amount)))
      # change increment(:points, amount: arg(:amount))
    end

    update :add_commission do
      argument :amount, :integer, allow_nil?: false
      change increment(:commission, amount: arg(:amount))
    end

    update :subtract_commission do
      argument :amount, :integer, allow_nil?: false
      change atomic_update(:commission, expr(commission - ^arg(:amount)))
    end
  end

  # 开放访问权限，允许其他用户查询积分
  policies do
    policy always() do
      authorize_if always()
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
    end

    attribute :points, :integer do
      default 0
      allow_nil? false
      public? true
    end

    attribute :commission, :integer do
      default 0
      allow_nil? false
      public? true
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      primary_key? true
      source_attribute :user_id
      destination_attribute :id
      allow_nil? false
    end
  end

  identities do
    identity :unique_user_id, [:user_id]
  end
end
