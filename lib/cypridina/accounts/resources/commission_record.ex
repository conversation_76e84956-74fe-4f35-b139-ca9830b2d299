defmodule Cypridina.Accounts.CommissionRecord do
  @moduledoc """
  抽水记录模型 - 记录所有代理抽水交易
  """
  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.Accounts,
    authorizers: [Ash.Policy.Authorizer],
    data_layer: AshPostgres.DataLayer

  postgres do
    table "commission_records"
    repo Cypridina.Repo
  end

  code_interface do
    define :create_record, action: :create
    define :get_by_agent, action: :get_by_agent
    define :get_by_subordinate, action: :get_by_subordinate
    define :get_by_transaction, action: :get_by_transaction
    define :get_agent_earnings, action: :get_agent_earnings
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true

      accept [
        :agent_id,
        :subordinate_id,
        :transaction_type,
        :transaction_id,
        :original_amount,
        :commission_rate,
        :commission_amount
      ]

      argument :agent_id, :uuid, allow_nil?: false
      argument :subordinate_id, :uuid, allow_nil?: false
      argument :transaction_type, :string, allow_nil?: false
      argument :transaction_id, :uuid, allow_nil?: false
      argument :original_amount, :integer, allow_nil?: false
      argument :commission_rate, :decimal, allow_nil?: false
      argument :commission_amount, :integer, allow_nil?: false

      change set_attribute(:agent_id, arg(:agent_id))
      change set_attribute(:subordinate_id, arg(:subordinate_id))
      change set_attribute(:transaction_type, arg(:transaction_type))
      change set_attribute(:transaction_id, arg(:transaction_id))
      change set_attribute(:original_amount, arg(:original_amount))
      change set_attribute(:commission_rate, arg(:commission_rate))
      change set_attribute(:commission_amount, arg(:commission_amount))
    end

    read :get_by_agent do
      argument :agent_id, :uuid, allow_nil?: false
      filter expr(agent_id == ^arg(:agent_id))
      prepare build(sort: [inserted_at: :desc])
    end

    read :get_by_subordinate do
      argument :subordinate_id, :uuid, allow_nil?: false
      filter expr(subordinate_id == ^arg(:subordinate_id))
      prepare build(sort: [inserted_at: :desc])
    end

    read :get_by_transaction do
      argument :transaction_type, :string, allow_nil?: false
      argument :transaction_id, :uuid, allow_nil?: false

      filter expr(
               transaction_type == ^arg(:transaction_type) and
                 transaction_id == ^arg(:transaction_id)
             )
    end

    read :get_agent_earnings do
      argument :agent_id, :uuid, allow_nil?: false
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true

      filter expr(agent_id == ^arg(:agent_id))
      prepare build(sort: [inserted_at: :desc])
    end
  end

  policies do
    # 暂时允许所有操作，简化权限
    policy always() do
      authorize_if always()
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :agent_id, :uuid do
      allow_nil? false
      public? true
      description "代理用户ID"
    end

    attribute :subordinate_id, :uuid do
      allow_nil? false
      public? true
      description "下线用户ID"
    end

    attribute :transaction_type, :string do
      allow_nil? false
      public? true
      description "交易类型：bet(投注), stock_buy(买入股票), stock_sell(卖出股票)"
    end

    attribute :transaction_id, :uuid do
      allow_nil? false
      public? true
      description "关联的交易ID"
    end

    attribute :original_amount, :integer do
      allow_nil? false
      public? true
      description "原始交易金额"
    end

    attribute :commission_rate, :decimal do
      allow_nil? false
      public? true
      description "抽水比例"

      constraints min: Decimal.new("0"),
                  max: Decimal.new("1")
    end

    attribute :commission_amount, :integer do
      allow_nil? false
      public? true
      description "抽水金额"
    end

    attribute :status, :integer do
      allow_nil? false
      default 1
      public? true
      description "记录状态：1-有效，0-无效"
    end

    timestamps()
  end

  relationships do
    belongs_to :agent, Cypridina.Accounts.User do
      public? true
      source_attribute :agent_id
      destination_attribute :id
    end

    belongs_to :subordinate, Cypridina.Accounts.User do
      public? true
      source_attribute :subordinate_id
      destination_attribute :id
    end
  end

  calculations do
    # 计算代理的总收益
    calculate :total_commission, :integer, expr(sum(commission_amount))
  end
end
