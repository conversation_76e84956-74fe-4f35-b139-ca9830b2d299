defmodule Cypridina.Ecto.Accounts.ECommissionRecord do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query
  alias Cypridina.Repo

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          agent_id: Ecto.UUID.t(),
          subordinate_id: Ecto.UUID.t(),
          transaction_type: String.t(),
          transaction_id: Ecto.UUID.t(),
          original_amount: Decimal.t(),
          commission_rate: Decimal.t(),
          commission_amount: Decimal.t(),
          status: integer(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "commission_records" do
    field :transaction_type, :string
    field :transaction_id, :binary_id
    field :original_amount, :decimal
    field :commission_rate, :decimal
    field :commission_amount, :decimal
    field :status, :integer, default: 1

    # 关联关系
    belongs_to :agent, Cypridina.Ecto.Accounts.EUser
    belongs_to :subordinate, Cypridina.Ecto.Accounts.EUser

    timestamps()
  end

  @doc """
  创建抽水记录的基本changeset
  """
  def changeset(commission_record, attrs) do
    commission_record
    |> cast(attrs, [
      :agent_id,
      :subordinate_id,
      :transaction_type,
      :transaction_id,
      :original_amount,
      :commission_rate,
      :commission_amount,
      :status
    ])
    |> validate_required([
      :agent_id,
      :subordinate_id,
      :transaction_type,
      :transaction_id,
      :original_amount,
      :commission_rate,
      :commission_amount
    ])
    |> validate_inclusion(:transaction_type, ["bet", "stock_buy", "stock_sell", "other"])
    |> validate_inclusion(:status, [0, 1])
    |> validate_amounts()
  end

  @doc """
  创建新抽水记录的changeset
  """
  def create_changeset(commission_record, attrs) do
    commission_record
    |> cast(attrs, [
      :agent_id,
      :subordinate_id,
      :transaction_type,
      :transaction_id,
      :original_amount,
      :commission_rate
    ])
    |> validate_required([
      :agent_id,
      :subordinate_id,
      :transaction_type,
      :transaction_id,
      :original_amount,
      :commission_rate
    ])
    |> validate_inclusion(:transaction_type, ["bet", "stock_buy", "stock_sell", "other"])
    |> put_change(:status, 1)
    |> calculate_commission_amount()
    |> validate_amounts()
  end

  # 计算抽水金额
  defp calculate_commission_amount(changeset) do
    original_amount = get_field(changeset, :original_amount)
    commission_rate = get_field(changeset, :commission_rate)

    if original_amount && commission_rate do
      commission_amount = Decimal.mult(original_amount, commission_rate)
      put_change(changeset, :commission_amount, commission_amount)
    else
      changeset
    end
  end

  # 验证金额
  defp validate_amounts(changeset) do
    changeset
    |> validate_change(:original_amount, fn :original_amount, amount ->
      case Decimal.compare(amount, Decimal.new("0")) do
        :lt -> [original_amount: "原始金额不能小于0"]
        _ -> []
      end
    end)
    |> validate_change(:commission_rate, fn :commission_rate, rate ->
      case Decimal.compare(rate, Decimal.new("0")) do
        :lt ->
          [commission_rate: "抽水比例不能小于0"]

        _ ->
          case Decimal.compare(rate, Decimal.new("1")) do
            :gt -> [commission_rate: "抽水比例不能大于100%"]
            _ -> []
          end
      end
    end)
    |> validate_change(:commission_amount, fn :commission_amount, amount ->
      case Decimal.compare(amount, Decimal.new("0")) do
        :lt -> [commission_amount: "抽水金额不能小于0"]
        _ -> []
      end
    end)
  end

  @doc """
  通过代理ID查找抽水记录
  """
  def get_by_agent(agent_id, opts \\ []) when is_binary(agent_id) do
    query =
      from(cr in __MODULE__,
        where: cr.agent_id == ^agent_id and cr.status == 1,
        order_by: [desc: cr.inserted_at]
      )

    query = apply_filters(query, opts)

    case Keyword.get(opts, :limit) do
      nil -> Repo.all(query)
      limit -> query |> limit(^limit) |> Repo.all()
    end
  end

  @doc """
  通过下线ID查找抽水记录
  """
  def get_by_subordinate(subordinate_id, opts \\ []) when is_binary(subordinate_id) do
    query =
      from(cr in __MODULE__,
        where: cr.subordinate_id == ^subordinate_id and cr.status == 1,
        order_by: [desc: cr.inserted_at]
      )

    query = apply_filters(query, opts)

    case Keyword.get(opts, :limit) do
      nil -> Repo.all(query)
      limit -> query |> limit(^limit) |> Repo.all()
    end
  end

  @doc """
  通过交易ID查找抽水记录
  """
  def get_by_transaction(transaction_id) when is_binary(transaction_id) do
    from(cr in __MODULE__,
      where: cr.transaction_id == ^transaction_id and cr.status == 1
    )
    |> Repo.all()
  end

  # 应用过滤条件
  defp apply_filters(query, opts) do
    Enum.reduce(opts, query, fn
      {:transaction_type, type}, query ->
        from(cr in query, where: cr.transaction_type == ^type)

      {:date_from, date}, query ->
        from(cr in query, where: cr.inserted_at >= ^date)

      {:date_to, date}, query ->
        from(cr in query, where: cr.inserted_at <= ^date)

      _, query ->
        query
    end)
  end

  @doc """
  获取代理的总收益
  """
  def get_agent_earnings(agent_id, opts \\ []) when is_binary(agent_id) do
    query =
      from(cr in __MODULE__,
        where: cr.agent_id == ^agent_id and cr.status == 1,
        select: sum(cr.commission_amount)
      )

    query = apply_filters(query, opts)

    case Repo.one(query) do
      nil -> Decimal.new("0")
      amount -> amount
    end
  end

  @doc """
  获取代理在指定时间段的收益统计
  """
  def get_agent_earnings_by_period(agent_id, date_from, date_to)
      when is_binary(agent_id) do
    from(cr in __MODULE__,
      where:
        cr.agent_id == ^agent_id and cr.status == 1 and
          cr.inserted_at >= ^date_from and cr.inserted_at <= ^date_to,
      group_by: cr.transaction_type,
      select: {cr.transaction_type, sum(cr.commission_amount)}
    )
    |> Repo.all()
    |> Enum.into(%{})
  end

  @doc """
  创建抽水记录
  """
  def create_record(attrs) do
    %__MODULE__{}
    |> create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  批量创建抽水记录
  """
  def create_records(records_attrs) when is_list(records_attrs) do
    records =
      Enum.map(records_attrs, fn attrs ->
        %__MODULE__{}
        |> create_changeset(attrs)
        |> apply_action!(:insert)
      end)

    Repo.insert_all(__MODULE__, records, returning: true)
  end

  @doc """
  停用抽水记录
  """
  def deactivate_record(record_id) when is_binary(record_id) do
    case Repo.get(__MODULE__, record_id) do
      nil ->
        {:error, "抽水记录不存在"}

      record ->
        record
        |> changeset(%{status: 0})
        |> Repo.update()
    end
  end

  @doc """
  根据交易创建抽水记录
  """
  def create_from_transaction(
        agent_id,
        subordinate_id,
        transaction_type,
        transaction_id,
        amount,
        commission_rate
      )
      when is_binary(agent_id) and is_binary(subordinate_id) and
             is_binary(transaction_type) and is_binary(transaction_id) do
    attrs = %{
      agent_id: agent_id,
      subordinate_id: subordinate_id,
      transaction_type: transaction_type,
      transaction_id: transaction_id,
      original_amount: amount,
      commission_rate: commission_rate
    }

    create_record(attrs)
  end
end
