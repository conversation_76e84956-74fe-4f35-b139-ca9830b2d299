defmodule Cypridina.Ecto.Accounts.EUserAsset do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query
  alias Cypridina.Repo

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          user_id: Ecto.UUID.t(),
          points: integer(),
          commission: integer(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "user_assets" do
    field :points, :integer, default: 0
    field :commission, :integer, default: 0

    # 关联关系
    belongs_to :user, Cypridina.Ecto.Accounts.EUser

    timestamps()
  end

  @doc """
  创建用户资产的基本changeset
  """
  def changeset(user_asset, attrs) do
    user_asset
    |> cast(attrs, [:user_id, :points, :commission])
    |> validate_required([:user_id, :points, :commission])
    |> validate_number(:points, greater_than_or_equal_to: 0)
    |> validate_number(:commission, greater_than_or_equal_to: 0)
    |> unique_constraint(:user_id, name: :user_assets_unique_user_id_index)
  end

  @doc """
  创建新用户资产的changeset
  """
  def create_changeset(user_asset, attrs) do
    user_asset
    |> cast(attrs, [:user_id, :points, :commission])
    |> validate_required([:user_id])
    |> put_change(:points, attrs[:points] || 0)
    |> put_change(:commission, attrs[:commission] || 0)
    |> validate_number(:points, greater_than_or_equal_to: 0)
    |> validate_number(:commission, greater_than_or_equal_to: 0)
    |> unique_constraint(:user_id, name: :user_assets_unique_user_id_index)
  end

  @doc """
  增加积分的changeset
  """
  def add_points_changeset(user_asset, amount) when is_integer(amount) and amount > 0 do
    user_asset
    |> change(points: user_asset.points + amount)
    |> validate_number(:points, greater_than_or_equal_to: 0)
  end

  @doc """
  减少积分的changeset
  """
  def subtract_points_changeset(user_asset, amount) when is_integer(amount) and amount > 0 do
    new_points = user_asset.points - amount

    user_asset
    |> change(points: new_points)
    |> validate_number(:points, greater_than_or_equal_to: 0, message: "积分不足")
  end

  @doc """
  通过用户ID查找用户资产
  """
  def get_by_user_id(user_id) when is_binary(user_id) do
    Repo.get_by(__MODULE__, user_id: user_id)
  end

  @doc """
  获取或创建用户资产
  """
  def get_or_create_user_asset(user_id) when is_binary(user_id) do
    case get_by_user_id(user_id) do
      nil ->
        case create_user_asset(%{user_id: user_id, points: 0}) do
          {:ok, user_asset} -> user_asset
          {:error, _changeset} -> nil
        end
      user_asset -> user_asset
    end
  end

  @doc """
  创建用户资产
  """
  def create_user_asset(attrs) do
    %__MODULE__{}
    |> create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  增加用户积分
  """
  def add_points(user_id, amount) when is_binary(user_id) and is_integer(amount) and amount > 0 do
    user_asset = get_or_create_user_asset(user_id)

    if user_asset do
      user_asset
      |> add_points_changeset(amount)
      |> Repo.update()
    else
      {:error, "用户资产不存在"}
    end
  end

  @doc """
  减少用户积分
  """
  def subtract_points(user_id, amount) when is_binary(user_id) and is_integer(amount) and amount > 0 do
    user_asset = get_by_user_id(user_id)

    if user_asset do
      user_asset
      |> subtract_points_changeset(amount)
      |> Repo.update()
    else
      {:error, "用户资产不存在"}
    end
  end

  @doc """
  获取用户积分
  """
  def get_user_points(user_id) when is_binary(user_id) do
    case get_by_user_id(user_id) do
      nil -> 0
      user_asset -> user_asset.points
    end
  end

  @doc """
  检查用户是否有足够积分
  """
  def has_enough_points?(user_id, amount) when is_binary(user_id) and is_integer(amount) do
    get_user_points(user_id) >= amount
  end

  @doc """
  转移积分（从一个用户转给另一个用户）
  """
  def transfer_points(from_user_id, to_user_id, amount)
      when is_binary(from_user_id) and is_binary(to_user_id) and is_integer(amount) and amount > 0 do

    Repo.transaction(fn ->
      case subtract_points(from_user_id, amount) do
        {:ok, _} ->
          case add_points(to_user_id, amount) do
            {:ok, result} -> result
            {:error, reason} ->
              Repo.rollback(reason)
          end
        {:error, reason} ->
          Repo.rollback(reason)
      end
    end)
  end

  # ==================== 抽水相关方法 ====================

  @doc """
  增加抽水的changeset
  """
  def add_commission_changeset(user_asset, amount) when is_integer(amount) and amount > 0 do
    user_asset
    |> change(commission: user_asset.commission + amount)
    |> validate_number(:commission, greater_than_or_equal_to: 0)
  end

  @doc """
  减少抽水的changeset
  """
  def subtract_commission_changeset(user_asset, amount) when is_integer(amount) and amount > 0 do
    new_commission = user_asset.commission - amount

    user_asset
    |> change(commission: new_commission)
    |> validate_number(:commission, greater_than_or_equal_to: 0, message: "抽水余额不足")
  end

  @doc """
  增加用户抽水
  """
  def add_commission(user_id, amount) when is_binary(user_id) and is_integer(amount) and amount > 0 do
    user_asset = get_or_create_user_asset(user_id)

    if user_asset do
      user_asset
      |> add_commission_changeset(amount)
      |> Repo.update()
    else
      {:error, "用户资产不存在"}
    end
  end

  @doc """
  减少用户抽水
  """
  def subtract_commission(user_id, amount) when is_binary(user_id) and is_integer(amount) and amount > 0 do
    user_asset = get_by_user_id(user_id)

    if user_asset do
      user_asset
      |> subtract_commission_changeset(amount)
      |> Repo.update()
    else
      {:error, "用户资产不存在"}
    end
  end

  @doc """
  获取用户抽水余额
  """
  def get_user_commission(user_id) when is_binary(user_id) do
    case get_by_user_id(user_id) do
      nil -> 0
      user_asset -> user_asset.commission
    end
  end

  @doc """
  检查用户是否有足够抽水余额
  """
  def has_enough_commission?(user_id, amount) when is_binary(user_id) and is_integer(amount) do
    get_user_commission(user_id) >= amount
  end

  @doc """
  转移抽水（从一个用户转给另一个用户）
  """
  def transfer_commission(from_user_id, to_user_id, amount)
      when is_binary(from_user_id) and is_binary(to_user_id) and is_integer(amount) and amount > 0 do

    Repo.transaction(fn ->
      case subtract_commission(from_user_id, amount) do
        {:ok, _} ->
          case add_commission(to_user_id, amount) do
            {:ok, result} -> result
            {:error, reason} ->
              Repo.rollback(reason)
          end
        {:error, reason} ->
          Repo.rollback(reason)
      end
    end)
  end

  @doc """
  抽水转换为积分
  """
  def commission_to_points(user_id, amount) when is_binary(user_id) and is_integer(amount) and amount > 0 do
    Repo.transaction(fn ->
      case subtract_commission(user_id, amount) do
        {:ok, _} ->
          case add_points(user_id, amount) do
            {:ok, result} -> result
            {:error, reason} ->
              Repo.rollback(reason)
          end
        {:error, reason} ->
          Repo.rollback(reason)
      end
    end)
  end
end
