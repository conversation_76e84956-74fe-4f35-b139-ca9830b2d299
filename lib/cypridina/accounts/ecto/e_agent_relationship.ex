defmodule Cypridina.Ecto.Accounts.EAgentRelationship do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query
  alias Cypridina.Repo

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          agent_id: Ecto.UUID.t(),
          subordinate_id: Ecto.UUID.t(),
          level: integer(),
          commission_rate: Decimal.t(),
          status: integer(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "agent_relationships" do
    field :level, :integer, default: 1
    field :commission_rate, :decimal, default: Decimal.new("0.05")
    field :status, :integer, default: 1

    # 关联关系
    belongs_to :agent, Cypridina.Ecto.Accounts.EUser, foreign_key: :agent_id
    belongs_to :subordinate, Cypridina.Ecto.Accounts.EUser, foreign_key: :subordinate_id

    timestamps()
  end

  @doc """
  创建代理关系的基本changeset
  """
  def changeset(agent_relationship, attrs) do
    agent_relationship
    |> cast(attrs, [:agent_id, :subordinate_id, :level, :commission_rate, :status])
    |> validate_required([:agent_id, :subordinate_id, :level, :commission_rate, :status])
    |> validate_number(:level, greater_than: 0)
    |> validate_commission_rate()
    |> validate_inclusion(:status, [0, 1])
    |> validate_not_self_agent()
    |> unique_constraint([:agent_id, :subordinate_id],
      name: :agent_relationships_agent_id_subordinate_id_index,
      message: "该代理关系已存在"
    )
  end

  @doc """
  创建新代理关系的changeset
  """
  def create_changeset(agent_relationship, attrs) do
    agent_relationship
    |> cast(attrs, [:agent_id, :subordinate_id, :level, :commission_rate])
    |> validate_required([:agent_id, :subordinate_id])
    |> put_change(:level, attrs[:level] || 1)
    |> put_change(:commission_rate, attrs[:commission_rate] || Decimal.new("0.05"))
    |> put_change(:status, 1)
    |> validate_number(:level, greater_than: 0)
    |> validate_commission_rate()
    |> validate_not_self_agent()
    |> validate_unique_relationship()
  end

  @doc """
  更新代理关系的changeset
  """
  def update_changeset(agent_relationship, attrs) do
    agent_relationship
    |> cast(attrs, [:level, :commission_rate, :status])
    |> validate_number(:level, greater_than: 0)
    |> validate_commission_rate()
    |> validate_inclusion(:status, [0, 1])
  end

  # 验证抽水比例
  defp validate_commission_rate(changeset) do
    changeset
    |> validate_change(:commission_rate, fn :commission_rate, rate ->
      case Decimal.compare(rate, Decimal.new("0")) do
        :lt ->
          [commission_rate: "抽水比例不能小于0"]

        _ ->
          case Decimal.compare(rate, Decimal.new("1")) do
            :gt -> [commission_rate: "抽水比例不能大于100%"]
            _ -> []
          end
      end
    end)
  end

  # 验证不能自己做自己的代理
  defp validate_not_self_agent(changeset) do
    agent_id = get_field(changeset, :agent_id)
    subordinate_id = get_field(changeset, :subordinate_id)

    if agent_id && subordinate_id && agent_id == subordinate_id do
      add_error(changeset, :subordinate_id, "不能将自己设为下线")
    else
      changeset
    end
  end

  # 验证代理关系的唯一性
  defp validate_unique_relationship(changeset) do
    agent_id = get_field(changeset, :agent_id)
    subordinate_id = get_field(changeset, :subordinate_id)

    if agent_id && subordinate_id do
      case Repo.get_by(__MODULE__, agent_id: agent_id, subordinate_id: subordinate_id) do
        nil -> changeset
        _existing -> add_error(changeset, :subordinate_id, "该代理关系已存在")
      end
    else
      changeset
    end
  end

  @doc """
  通过代理ID查找所有下线关系
  """
  def get_by_agent(agent_id) when is_binary(agent_id) do
    from(ar in __MODULE__,
      where: ar.agent_id == ^agent_id and ar.status == 1,
      order_by: [asc: ar.level, asc: ar.inserted_at]
    )
    |> Repo.all()
  end

  @doc """
  通过下线ID查找代理关系
  """
  def get_by_subordinate(subordinate_id) when is_binary(subordinate_id) do
    from(ar in __MODULE__,
      where: ar.subordinate_id == ^subordinate_id and ar.status == 1
    )
    |> Repo.one()
  end

  @doc """
  检查代理关系是否存在
  """
  def relationship_exists?(agent_id, subordinate_id)
      when is_binary(agent_id) and is_binary(subordinate_id) do
    from(ar in __MODULE__,
      where: ar.agent_id == ^agent_id and ar.subordinate_id == ^subordinate_id and ar.status == 1
    )
    |> Repo.exists?()
  end

  @doc """
  获取代理树（包括多级下线）
  """
  def get_agent_tree(agent_id) when is_binary(agent_id) do
    from(ar in __MODULE__,
      where: ar.agent_id == ^agent_id and ar.status == 1,
      order_by: [asc: ar.level, asc: ar.inserted_at],
      preload: [:subordinate]
    )
    |> Repo.all()
  end

  @doc """
  创建代理关系
  """
  def create_relationship(attrs) do
    %__MODULE__{}
    |> create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  更新代理关系
  """
  def update_relationship(agent_relationship, attrs) do
    agent_relationship
    |> update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  停用代理关系
  """
  def deactivate_relationship(agent_id, subordinate_id)
      when is_binary(agent_id) and is_binary(subordinate_id) do
    case Repo.get_by(__MODULE__, agent_id: agent_id, subordinate_id: subordinate_id) do
      nil ->
        {:error, "代理关系不存在"}

      relationship ->
        relationship
        |> update_changeset(%{status: 0})
        |> Repo.update()
    end
  end

  @doc """
  激活代理关系
  """
  def activate_relationship(agent_id, subordinate_id)
      when is_binary(agent_id) and is_binary(subordinate_id) do
    case Repo.get_by(__MODULE__, agent_id: agent_id, subordinate_id: subordinate_id) do
      nil ->
        {:error, "代理关系不存在"}

      relationship ->
        relationship
        |> update_changeset(%{status: 1})
        |> Repo.update()
    end
  end

  @doc """
  获取代理的抽水比例
  """
  def get_commission_rate(agent_id, subordinate_id)
      when is_binary(agent_id) and is_binary(subordinate_id) do
    case Repo.get_by(__MODULE__, agent_id: agent_id, subordinate_id: subordinate_id, status: 1) do
      nil -> Decimal.new("0")
      relationship -> relationship.commission_rate
    end
  end
end
