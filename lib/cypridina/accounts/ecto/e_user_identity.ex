defmodule Cypridina.Ecto.Accounts.EUserIdentity do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query
  alias Cypridina.Repo

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          user_id: Ecto.UUID.t(),
          strategy: String.t(),
          uid: String.t(),
          access_token: String.t() | nil,
          refresh_token: String.t() | nil,
          expires_at: DateTime.t() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "user_identities" do
    field :strategy, :string
    field :uid, :string
    field :access_token, :string
    field :refresh_token, :string
    field :expires_at, :utc_datetime

    # 关联关系
    belongs_to :user, Cypridina.Ecto.Accounts.EUser

    timestamps()
  end

  @doc """
  创建用户身份的基本changeset
  """
  def changeset(user_identity, attrs) do
    user_identity
    |> cast(attrs, [:user_id, :strategy, :uid, :access_token, :refresh_token, :expires_at])
    |> validate_required([:user_id, :strategy, :uid])
    |> validate_length(:strategy, min: 1)
    |> validate_length(:uid, min: 1)
    |> unique_constraint([:strategy, :uid],
      name: :user_identities_strategy_uid_index,
      message: "该身份已被其他用户使用"
    )
  end

  @doc """
  创建新用户身份的changeset
  """
  def create_changeset(user_identity, attrs) do
    user_identity
    |> cast(attrs, [:user_id, :strategy, :uid, :access_token, :refresh_token, :expires_at])
    |> validate_required([:user_id, :strategy, :uid])
    |> validate_length(:strategy, min: 1)
    |> validate_length(:uid, min: 1)
    |> validate_strategy()
    |> unique_constraint([:strategy, :uid],
      name: :user_identities_strategy_uid_index,
      message: "该身份已被其他用户使用"
    )
  end

  @doc """
  更新用户身份的changeset
  """
  def update_changeset(user_identity, attrs) do
    user_identity
    |> cast(attrs, [:access_token, :refresh_token, :expires_at])
  end

  # 验证策略类型
  defp validate_strategy(changeset) do
    validate_inclusion(
      changeset,
      :strategy,
      ["oauth2", "github", "google", "wechat", "qq", "weibo", "phone", "email"],
      message: "不支持的身份验证策略"
    )
  end

  @doc """
  通过策略和UID查找用户身份
  """
  def get_by_strategy_and_uid(strategy, uid) when is_binary(strategy) and is_binary(uid) do
    from(ui in __MODULE__,
      where: ui.strategy == ^strategy and ui.uid == ^uid
    )
    |> Repo.one()
  end

  @doc """
  通过用户ID查找所有身份
  """
  def get_by_user_id(user_id) when is_binary(user_id) do
    from(ui in __MODULE__,
      where: ui.user_id == ^user_id,
      order_by: [asc: ui.strategy, desc: ui.inserted_at]
    )
    |> Repo.all()
  end

  @doc """
  通过用户ID和策略查找身份
  """
  def get_by_user_id_and_strategy(user_id, strategy)
      when is_binary(user_id) and is_binary(strategy) do
    from(ui in __MODULE__,
      where: ui.user_id == ^user_id and ui.strategy == ^strategy
    )
    |> Repo.one()
  end

  @doc """
  检查身份是否存在
  """
  def identity_exists?(strategy, uid) when is_binary(strategy) and is_binary(uid) do
    from(ui in __MODULE__,
      where: ui.strategy == ^strategy and ui.uid == ^uid
    )
    |> Repo.exists?()
  end

  @doc """
  检查用户是否已绑定某种策略
  """
  def user_has_strategy?(user_id, strategy)
      when is_binary(user_id) and is_binary(strategy) do
    from(ui in __MODULE__,
      where: ui.user_id == ^user_id and ui.strategy == ^strategy
    )
    |> Repo.exists?()
  end

  @doc """
  获取过期的令牌
  """
  def get_expired_tokens do
    now = DateTime.utc_now()

    from(ui in __MODULE__,
      where: not is_nil(ui.expires_at) and ui.expires_at < ^now
    )
    |> Repo.all()
  end

  @doc """
  检查访问令牌是否过期
  """
  def token_expired?(%__MODULE__{expires_at: nil}), do: false

  def token_expired?(%__MODULE__{expires_at: expires_at}) do
    DateTime.compare(expires_at, DateTime.utc_now()) == :lt
  end

  @doc """
  创建用户身份
  """
  def create_identity(attrs) do
    %__MODULE__{}
    |> create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  更新用户身份
  """
  def update_identity(user_identity, attrs) do
    user_identity
    |> update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  删除用户身份
  """
  def delete_identity(user_identity) do
    Repo.delete(user_identity)
  end

  @doc """
  通过策略和UID删除身份
  """
  def delete_by_strategy_and_uid(strategy, uid)
      when is_binary(strategy) and is_binary(uid) do
    case get_by_strategy_and_uid(strategy, uid) do
      nil -> {:error, "身份不存在"}
      identity -> delete_identity(identity)
    end
  end

  @doc """
  删除用户的所有身份
  """
  def delete_all_for_user(user_id) when is_binary(user_id) do
    from(ui in __MODULE__, where: ui.user_id == ^user_id)
    |> Repo.delete_all()
  end

  @doc """
  删除用户的特定策略身份
  """
  def delete_user_strategy(user_id, strategy)
      when is_binary(user_id) and is_binary(strategy) do
    case get_by_user_id_and_strategy(user_id, strategy) do
      nil -> {:error, "身份不存在"}
      identity -> delete_identity(identity)
    end
  end

  @doc """
  刷新访问令牌
  """
  def refresh_access_token(user_identity, new_access_token, new_expires_at \\ nil) do
    attrs = %{access_token: new_access_token}
    attrs = if new_expires_at, do: Map.put(attrs, :expires_at, new_expires_at), else: attrs

    update_identity(user_identity, attrs)
  end

  @doc """
  绑定新的身份到用户
  """
  def bind_identity_to_user(user_id, strategy, uid, opts \\ [])
      when is_binary(user_id) and is_binary(strategy) and is_binary(uid) do
    # 检查身份是否已被其他用户使用
    case get_by_strategy_and_uid(strategy, uid) do
      nil ->
        attrs = %{
          user_id: user_id,
          strategy: strategy,
          uid: uid,
          access_token: Keyword.get(opts, :access_token),
          refresh_token: Keyword.get(opts, :refresh_token),
          expires_at: Keyword.get(opts, :expires_at)
        }

        create_identity(attrs)

      %{user_id: ^user_id} ->
        {:error, "该身份已绑定到当前用户"}

      _ ->
        {:error, "该身份已被其他用户使用"}
    end
  end

  @doc """
  解绑用户身份
  """
  def unbind_identity_from_user(user_id, strategy)
      when is_binary(user_id) and is_binary(strategy) do
    delete_user_strategy(user_id, strategy)
  end

  @doc """
  获取身份统计信息
  """
  def get_identity_stats do
    strategy_stats =
      from(ui in __MODULE__,
        group_by: ui.strategy,
        select: {ui.strategy, count(ui.id)}
      )
      |> Repo.all()
      |> Enum.into(%{})

    total = from(ui in __MODULE__, select: count(ui.id)) |> Repo.one() || 0

    %{
      total: total,
      by_strategy: strategy_stats
    }
  end
end
