defmodule Cypridina.Ecto.Accounts.EUser do
  use Ecto.Schema
  import Ecto.Changeset
  alias <PERSON><PERSON><PERSON><PERSON>.Repo

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          numeric_id: integer(),
          username: String.t() | nil,
          email: String.t() | nil,
          hashed_password: String.t(),
          confirmed_at: DateTime.t() | nil,
          agent_level: integer(),
          created_by_agent: Ecto.UUID.t() | nil,
          permission_level: integer(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "users" do
    # 数字ID属性
    field :numeric_id, :integer

    # 用户名属性
    field :username, :string

    # 电子邮件属性
    field :email, :string

    # 哈希密码
    field :hashed_password, :string

    # 账户确认时间
    field :confirmed_at, :utc_datetime_usec

    # 代理系统相关字段
    field :agent_level, :integer, default: -1
    field :created_by_agent, :binary_id

    # 用户权限级别字段
    field :permission_level, :integer, default: 0

    # 虚拟属性，不存储在数据库中
    field :password, :string, virtual: true
    field :password_confirmation, :string, virtual: true
    field :display_id, :string, virtual: true
    field :points, :integer, virtual: true

    # 计算字段（虚拟字段）
    field :is_agent, :boolean, virtual: true
    field :is_admin, :boolean, virtual: true
    field :is_super_admin, :boolean, virtual: true
    field :is_root_agent, :boolean, virtual: true
    field :role_name, :string, virtual: true
    field :agent_level_name, :string, virtual: true
    field :can_access_admin, :boolean, virtual: true

    # 关联关系
    has_one :asset, Cypridina.Ecto.Accounts.EUserAsset, foreign_key: :user_id

    has_many :agent_relationships, Cypridina.Ecto.Accounts.EAgentRelationship,
      foreign_key: :agent_id

    has_many :subordinate_relationships, Cypridina.Ecto.Accounts.EAgentRelationship,
      foreign_key: :subordinate_id

    # 通过代理关系表关联的下线用户
    has_many :subordinates, through: [:agent_relationships, :subordinate]

    # 通过代理关系表关联的上级代理
    has_many :agents, through: [:subordinate_relationships, :agent]

    has_many :commission_records_as_agent, Cypridina.Ecto.Accounts.ECommissionRecord,
      foreign_key: :agent_id

    has_many :commission_records_as_subordinate, Cypridina.Ecto.Accounts.ECommissionRecord,
      foreign_key: :subordinate_id

    belongs_to :creator_agent, __MODULE__,
      foreign_key: :created_by_agent,
      references: :id,
      define_field: false

    timestamps()
  end

  @doc """
  创建用户的基本changeset
  """
  def changeset(user, attrs) do
    user
    |> cast(attrs, [
      :numeric_id,
      :username,
      :email,
      :hashed_password,
      :confirmed_at,
      :agent_level,
      :created_by_agent,
      :permission_level
    ])
    |> validate_required([:numeric_id, :username, :hashed_password])
    |> unique_constraint(:email, name: :users_unique_email_index)
    |> unique_constraint(:username, name: :users_unique_username_index)
    |> unique_constraint(:numeric_id, name: :users_unique_numeric_id_index)
    |> validate_username()
    |> validate_agent_fields()
    |> validate_permission_level()
    |> populate_calculated_fields()
  end

  @doc """
  创建新用户的changeset
  """
  def registration_changeset(user, attrs) do
    user
    |> cast(attrs, [:username, :password, :password_confirmation])
    |> validate_required([:username, :password, :password_confirmation])
    |> validate_username()
    |> validate_password()
    |> validate_confirmation(:password)
    |> maybe_hash_password()
    |> maybe_generate_numeric_id()
  end

  @doc """
  修改密码的changeset
  """
  def password_changeset(user, attrs) do
    user
    |> cast(attrs, [:password, :password_confirmation, :current_password])
    |> validate_required([:password, :password_confirmation, :current_password])
    |> validate_password()
    |> validate_current_password(attrs)
    |> validate_confirmation(:password)
    |> maybe_hash_password()
  end

  @doc """
  重置密码的changeset
  """
  def reset_password_changeset(user, attrs) do
    user
    |> cast(attrs, [:password, :password_confirmation])
    |> validate_required([:password, :password_confirmation])
    |> validate_password()
    |> validate_confirmation(:password)
    |> maybe_hash_password()
  end

  defp validate_username(changeset) do
    changeset
    |> validate_length(:username, min: 3, max: 20)
    |> validate_format(:username, ~r/^[a-zA-Z0-9_]+$/, message: "用户名只能包含字母、数字和下划线")
  end

  defp validate_password(changeset) do
    changeset
    |> validate_length(:password,
      min: 6,
      message: "密码长度必须至少为6个字符"
    )
  end

  defp validate_agent_fields(changeset) do
    changeset
    |> validate_number(:agent_level, greater_than_or_equal_to: -1)
  end

  defp validate_permission_level(changeset) do
    changeset
    |> validate_number(:permission_level, greater_than_or_equal_to: 0, less_than_or_equal_to: 2)
  end

  defp validate_current_password(changeset, %{current_password: current_password}) do
    if valid_password?(changeset.data, current_password) do
      changeset
    else
      add_error(changeset, :current_password, "当前密码不正确")
    end
  end

  defp validate_current_password(changeset, _), do: changeset

  defp maybe_hash_password(changeset) do
    if password = get_change(changeset, :password) do
      # 使用bcrypt哈希密码
      changeset
      |> put_change(:hashed_password, Bcrypt.hash_pwd_salt(password))
      |> delete_change(:password)
      |> delete_change(:password_confirmation)
    else
      changeset
    end
  end

  defp maybe_generate_numeric_id(changeset) do
    case Ecto.Adapters.SQL.query(Cypridina.Repo, "SELECT nextval('user_numeric_id_seq')") do
      {:ok, %{rows: [[next_id]]}} ->
        put_change(changeset, :numeric_id, next_id)

      _ ->
        add_error(changeset, :numeric_id, "无法生成用户ID")
    end
  end

  defp populate_calculated_fields(changeset) do
    # 获取字段值
    agent_level = get_field(changeset, :agent_level, -1)
    permission_level = get_field(changeset, :permission_level, 0)
    numeric_id = get_field(changeset, :numeric_id)

    changeset
    |> put_change(:is_agent, agent_level >= 0)
    |> put_change(:is_admin, permission_level >= 1)
    |> put_change(:is_super_admin, permission_level == 2)
    |> put_change(:is_root_agent, agent_level == 0)
    |> put_change(:role_name, get_role_name(permission_level))
    |> put_change(:agent_level_name, get_agent_level_name(agent_level))
    |> put_change(:can_access_admin, permission_level >= 1 or agent_level >= 0)
    |> put_change(:display_id, if(numeric_id, do: "用户#{numeric_id}", else: nil))
  end

  defp get_role_name(permission_level) do
    case permission_level do
      2 -> "超级管理员"
      1 -> "管理员"
      0 -> "普通用户"
      _ -> "未知"
    end
  end

  defp get_agent_level_name(agent_level) do
    case agent_level do
      -1 -> "普通用户"
      0 -> "根代理"
      level when level > 0 -> "#{level}级代理"
      _ -> "未知"
    end
  end

  @doc """
  验证密码是否正确
  """
  def valid_password?(%__MODULE__{hashed_password: hashed_password}, password)
      when is_binary(hashed_password) and is_binary(password) do
    Bcrypt.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _), do: false

  @doc """
  通过用户名查找用户
  """
  def get_by_username(username) when is_binary(username) do
    Repo.get_by(__MODULE__, username: String.downcase(username))
  end

  @doc """
  通过数字ID查找用户
  """
  def get_by_numeric_id(numeric_id) when is_integer(numeric_id) do
    Repo.get_by(__MODULE__, numeric_id: numeric_id)
  end

  @doc """
  通过邮箱查找用户
  """
  def get_by_email(email) when is_binary(email) do
    Repo.get_by(__MODULE__, email: String.downcase(email))
  end

  @doc """
  获取用户的显示ID
  """
  def get_display_id(%__MODULE__{numeric_id: numeric_id}) do
    "用户#{numeric_id}"
  end

  @doc """
  获取用户的积分
  """
  def get_points(%__MODULE__{} = user) do
    user = Repo.preload(user, :asset)

    case user.asset do
      nil -> 0
      asset -> asset.points
    end
  end

  @doc """
  用户登录
  """
  def authenticate_user(username, password) when is_binary(username) and is_binary(password) do
    user = get_by_username(username)

    cond do
      user && valid_password?(user, password) ->
        {:ok, user}

      user ->
        {:error, "密码不正确"}

      true ->
        # 防止时序攻击，即使用户不存在也执行相同的计算
        Bcrypt.no_user_verify()
        {:error, "用户名不存在"}
    end
  end

  @doc """
  用户注册
  """
  def register(attrs) do
    %__MODULE__{}
    |> registration_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  修改密码
  """
  def update_password(user, attrs) do
    user
    |> password_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  重置密码
  """
  def reset_password(user, attrs) do
    user
    |> reset_password_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  填充用户的计算字段
  """
  def with_calculated_fields(%__MODULE__{} = user) do
    %{
      user
      | is_agent: user.agent_level >= 0,
        is_admin: user.permission_level >= 1,
        is_super_admin: user.permission_level == 2,
        is_root_agent: user.agent_level == 0,
        role_name: get_role_name(user.permission_level),
        agent_level_name: get_agent_level_name(user.agent_level),
        can_access_admin: user.permission_level >= 1 or user.agent_level >= 0,
        display_id: "用户#{user.numeric_id}"
    }
  end

  @doc """
  检查用户是否为代理
  """
  def is_agent?(%__MODULE__{agent_level: agent_level}), do: agent_level >= 0

  @doc """
  检查用户是否为管理员
  """
  def is_admin?(%__MODULE__{permission_level: permission_level}), do: permission_level >= 1

  @doc """
  检查用户是否为超级管理员
  """
  def is_super_admin?(%__MODULE__{permission_level: permission_level}), do: permission_level == 2

  @doc """
  检查用户是否为根代理
  """
  def is_root_agent?(%__MODULE__{agent_level: agent_level}), do: agent_level == 0

  @doc """
  检查用户是否可以访问后台管理
  """
  def can_access_admin?(%__MODULE__{permission_level: permission_level, agent_level: agent_level}) do
    permission_level >= 1 or agent_level >= 0
  end

  @doc """
  获取用户角色名称
  """
  def role_name(%__MODULE__{permission_level: permission_level}) do
    get_role_name(permission_level)
  end

  @doc """
  获取代理等级名称
  """
  def agent_level_name(%__MODULE__{agent_level: agent_level}) do
    get_agent_level_name(agent_level)
  end
end
