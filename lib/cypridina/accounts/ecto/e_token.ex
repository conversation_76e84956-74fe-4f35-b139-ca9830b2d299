defmodule Cypridina.Ecto.Accounts.EToken do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query
  alias Cypridina.Repo

  @type t :: %__MODULE__{
          jti: String.t(),
          subject: String.t(),
          expires_at: DateTime.t(),
          purpose: String.t(),
          extra_data: map(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:jti, :string, autogenerate: false}
  schema "tokens" do
    field :subject, :string
    field :expires_at, :utc_datetime
    field :purpose, :string
    field :extra_data, :map

    timestamps()
  end

  @doc """
  创建令牌的基本changeset
  """
  def changeset(token, attrs) do
    token
    |> cast(attrs, [:jti, :subject, :expires_at, :purpose, :extra_data])
    |> validate_required([:jti, :subject, :expires_at, :purpose])
    |> validate_length(:jti, min: 1)
    |> validate_length(:subject, min: 1)
    |> validate_length(:purpose, min: 1)
    |> unique_constraint(:jti, name: :tokens_pkey)
  end

  @doc """
  创建新令牌的changeset
  """
  def create_changeset(token, attrs) do
    token
    |> cast(attrs, [:jti, :subject, :expires_at, :purpose, :extra_data])
    |> validate_required([:jti, :subject, :expires_at, :purpose])
    |> validate_length(:jti, min: 1)
    |> validate_length(:subject, min: 1)
    |> validate_length(:purpose, min: 1)
    |> put_change(:extra_data, attrs[:extra_data] || %{})
    |> unique_constraint(:jti, name: :tokens_pkey)
  end

  @doc """
  通过JTI查找令牌
  """
  def get_by_jti(jti) when is_binary(jti) do
    Repo.get(__MODULE__, jti)
  end

  @doc """
  通过JTI和目的查找令牌
  """
  def get_by_jti_and_purpose(jti, purpose) when is_binary(jti) and is_binary(purpose) do
    from(t in __MODULE__,
      where: t.jti == ^jti and t.purpose == ^purpose
    )
    |> Repo.one()
  end

  @doc """
  通过令牌字符串查找令牌（需要解析JWT）
  """
  def get_by_token(token_string, purpose \\ nil) when is_binary(token_string) do
    # 这里需要解析JWT来获取JTI
    # 简化实现，实际应该使用JWT库来解析
    case extract_jti_from_token(token_string) do
      {:ok, jti} ->
        if purpose do
          get_by_jti_and_purpose(jti, purpose)
        else
          get_by_jti(jti)
        end

      {:error, _} ->
        nil
    end
  end

  @doc """
  查找所有过期的令牌
  """
  def get_expired_tokens do
    now = DateTime.utc_now()

    from(t in __MODULE__,
      where: t.expires_at < ^now
    )
    |> Repo.all()
  end

  @doc """
  通过主体查找令牌
  """
  def get_by_subject(subject, opts \\ []) when is_binary(subject) do
    query =
      from(t in __MODULE__,
        where: t.subject == ^subject,
        order_by: [desc: t.created_at]
      )

    query = apply_filters(query, opts)

    case Keyword.get(opts, :limit) do
      nil -> Repo.all(query)
      limit -> query |> limit(^limit) |> Repo.all()
    end
  end

  # 应用过滤条件
  defp apply_filters(query, opts) do
    Enum.reduce(opts, query, fn
      {:purpose, purpose}, query ->
        from(t in query, where: t.purpose == ^purpose)

      {:not_expired, true}, query ->
        now = DateTime.utc_now()
        from(t in query, where: t.expires_at > ^now)

      {:expired, true}, query ->
        now = DateTime.utc_now()
        from(t in query, where: t.expires_at <= ^now)

      _, query ->
        query
    end)
  end

  @doc """
  检查令牌是否过期
  """
  def expired?(%__MODULE__{expires_at: expires_at}) do
    DateTime.compare(expires_at, DateTime.utc_now()) == :lt
  end

  @doc """
  检查令牌是否有效（未过期）
  """
  def valid?(%__MODULE__{} = token) do
    not expired?(token)
  end

  @doc """
  创建令牌
  """
  def create_token(attrs) do
    %__MODULE__{}
    |> create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  存储令牌
  """
  def store_token(jti, subject, expires_at, purpose, extra_data \\ %{}) do
    attrs = %{
      jti: jti,
      subject: subject,
      expires_at: expires_at,
      purpose: purpose,
      extra_data: extra_data
    }

    create_token(attrs)
  end

  @doc """
  撤销令牌（删除）
  """
  def revoke_token(jti) when is_binary(jti) do
    case get_by_jti(jti) do
      nil -> {:error, "令牌不存在"}
      token -> Repo.delete(token)
    end
  end

  @doc """
  撤销主体的所有令牌
  """
  def revoke_all_for_subject(subject) when is_binary(subject) do
    from(t in __MODULE__, where: t.subject == ^subject)
    |> Repo.delete_all()
  end

  @doc """
  撤销主体的特定目的令牌
  """
  def revoke_all_for_subject_and_purpose(subject, purpose)
      when is_binary(subject) and is_binary(purpose) do
    from(t in __MODULE__,
      where: t.subject == ^subject and t.purpose == ^purpose
    )
    |> Repo.delete_all()
  end

  @doc """
  清理过期令牌
  """
  def expunge_expired do
    now = DateTime.utc_now()

    from(t in __MODULE__, where: t.expires_at < ^now)
    |> Repo.delete_all()
  end

  @doc """
  检查令牌是否被撤销（在这个简化实现中，删除即撤销）
  """
  def revoked?(jti) when is_binary(jti) do
    case get_by_jti(jti) do
      # 不存在即被撤销
      nil -> true
      # 过期也算撤销
      token -> expired?(token)
    end
  end

  @doc """
  获取令牌统计信息
  """
  def get_token_stats do
    now = DateTime.utc_now()

    total_query = from(t in __MODULE__, select: count(t.jti))
    expired_query = from(t in __MODULE__, where: t.expires_at < ^now, select: count(t.jti))

    total = Repo.one(total_query) || 0
    expired = Repo.one(expired_query) || 0

    %{
      total: total,
      expired: expired,
      active: total - expired
    }
  end

  # 从JWT令牌中提取JTI（简化实现）
  defp extract_jti_from_token(_token_string) do
    # 这里应该使用实际的JWT库来解析
    # 简化实现，假设JTI在payload中
    try do
      # 实际实现应该使用 JOSE 或其他JWT库
      {:ok, "extracted_jti"}
    rescue
      _ -> {:error, "无法解析令牌"}
    end
  end
end
