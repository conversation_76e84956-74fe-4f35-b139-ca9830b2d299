# Ecto 适配器

这个目录包含了为 BackPex 管理后台创建的 Ecto 适配器。由于 BackPex 只能使用 Ecto schema，而不能直接使用 Ash 资源，我们为每个 Ash 资源创建了对应的 Ecto 适配器。

## 适配器列表

### 1. EUser (`e_user.ex`)
- **对应 Ash 资源**: `Cypridina.Accounts.User`
- **功能**: 用户管理，包括注册、登录、密码管理等
- **特殊字段**: 
  - `numeric_id`: 用户数字ID
  - `is_agent`: 是否为代理
  - `agent_level`: 代理等级
  - `created_by_agent`: 创建者代理ID

### 2. EUserAsset (`e_user_asset.ex`)
- **对应 Ash 资源**: `Cypridina.Accounts.UserAsset`
- **功能**: 用户资产管理，积分系统
- **主要方法**:
  - `add_points/2`: 增加积分
  - `subtract_points/2`: 减少积分
  - `transfer_points/3`: 转移积分

### 3. EAgentRelationship (`e_agent_relationship.ex`)
- **对应 Ash 资源**: `Cypridina.Accounts.AgentRelationship`
- **功能**: 代理关系管理
- **主要字段**:
  - `agent_id`: 代理用户ID
  - `subordinate_id`: 下线用户ID
  - `commission_rate`: 抽水比例
  - `level`: 代理层级

### 4. ECommissionRecord (`e_commission_record.ex`)
- **对应 Ash 资源**: `Cypridina.Accounts.CommissionRecord`
- **功能**: 抽水记录管理
- **交易类型**:
  - `bet`: 投注抽水
  - `stock_buy`: 股票买入抽水
  - `stock_sell`: 股票卖出抽水
  - `other`: 其他类型

### 5. EAdmin (`e_admin.ex`)
- **对应 Ash 资源**: `Cypridina.Accounts.Admin`
- **功能**: 管理员账号管理
- **特殊字段**:
  - `is_root`: 是否为根管理员
  - 根管理员不能被删除

### 6. EToken (`e_token.ex`)
- **对应 Ash 资源**: `Cypridina.Accounts.Token`
- **功能**: 认证令牌管理
- **主要用途**:
  - `sign_in`: 登录令牌
  - `password_reset`: 密码重置令牌
  - `email_confirmation`: 邮箱确认令牌

### 7. EUserIdentity (`e_user_identity.ex`)
- **对应 Ash 资源**: `Cypridina.Accounts.UserIdentity`
- **功能**: 第三方登录身份管理
- **支持策略**:
  - `oauth2`, `github`, `google`, `wechat`, `qq`, `weibo`, `phone`, `email`

## LiveResource 配置

每个 Ecto 适配器都有对应的 LiveResource 配置：

```elixir
# 在 router.ex 中
live_resources "/users", Live.UserLive
live_resources "/user_assets", Live.UserAssetLive
live_resources "/agent_relationships", Live.AgentLive
live_resources "/commission_records", Live.CommissionRecordLive
live_resources "/admins", Live.AdminLive
live_resources "/tokens", Live.TokenLive
live_resources "/user_identities", Live.UserIdentityLive
```

## 使用方法

### 1. 基本 CRUD 操作

```elixir
# 创建用户
{:ok, user} = EUser.register(%{
  username: "test_user",
  password: "password123",
  password_confirmation: "password123"
})

# 查找用户
user = EUser.get_by_username("test_user")

# 更新用户积分
{:ok, _} = EUserAsset.add_points(user.id, 100)
```

### 2. 代理系统操作

```elixir
# 创建代理关系
{:ok, relationship} = EAgentRelationship.create_relationship(%{
  agent_id: agent_user.id,
  subordinate_id: subordinate_user.id,
  commission_rate: Decimal.new("0.05")
})

# 创建抽水记录
{:ok, record} = ECommissionRecord.create_from_transaction(
  agent_id,
  subordinate_id,
  "bet",
  transaction_id,
  Decimal.new("100"),
  Decimal.new("0.05")
)
```

### 3. 管理员操作

```elixir
# 创建管理员
{:ok, admin} = EAdmin.create_admin(%{
  username: "admin",
  password: "admin123",
  password_confirmation: "admin123",
  is_root: false
})

# 管理员登录
{:ok, admin} = EAdmin.authenticate_admin("admin", "admin123")
```

## 测试

运行测试以验证所有适配器是否正常工作：

```elixir
# 在 iex 中
Cypridina.Accounts.EctoAdapterTest.run_full_test()
```

## 注意事项

1. **数据一致性**: Ecto 适配器和 Ash 资源操作同一张表，需要确保数据一致性
2. **权限控制**: 每个 LiveResource 都有相应的权限控制
3. **关联关系**: 所有关联关系都已正确配置，支持预加载
4. **验证规则**: 所有验证规则都已从 Ash 资源迁移到 Ecto changeset

## 扩展

如果需要添加新的字段或功能：

1. 在对应的 Ecto schema 中添加字段
2. 更新 changeset 函数
3. 在 LiveResource 的 fields 配置中添加新字段
4. 更新相关的验证规则

这样就可以在 BackPex 管理后台中管理新的字段了。
