defmodule Cypridina.Ecto.Accounts.EAdmin do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query
  alias Cypridina.Repo

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          username: String.t(),
          hashed_password: String.t(),
          is_root: boolean(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "admins" do
    field :username, :string
    field :hashed_password, :string
    field :is_root, :boolean, default: false

    # 虚拟属性，不存储在数据库中
    field :password, :string, virtual: true
    field :password_confirmation, :string, virtual: true
    field :current_password, :string, virtual: true

    timestamps()
  end

  @doc """
  创建管理员的基本changeset
  """
  def changeset(admin, attrs) do
    admin
    |> cast(attrs, [:username, :hashed_password, :is_root])
    |> validate_required([:username, :hashed_password])
    |> validate_username()
    |> unique_constraint(:username, name: :admins_unique_username_index)
  end

  @doc """
  创建新管理员的changeset
  """
  def create_changeset(admin, attrs) do
    admin
    |> cast(attrs, [:username, :password, :password_confirmation, :is_root])
    |> validate_required([:username, :password, :password_confirmation])
    |> validate_username()
    |> validate_password()
    |> validate_confirmation(:password)
    |> put_change(:is_root, attrs[:is_root] || false)
    |> maybe_hash_password()
    |> unique_constraint(:username, name: :admins_unique_username_index)
  end

  @doc """
  修改密码的changeset
  """
  def password_changeset(admin, attrs) do
    admin
    |> cast(attrs, [:password, :password_confirmation, :current_password])
    |> validate_required([:password, :password_confirmation, :current_password])
    |> validate_password()
    |> validate_current_password(attrs)
    |> validate_confirmation(:password)
    |> maybe_hash_password()
  end

  @doc """
  重置密码的changeset（由根管理员操作）
  """
  def reset_password_changeset(admin, attrs) do
    admin
    |> cast(attrs, [:password, :password_confirmation])
    |> validate_required([:password, :password_confirmation])
    |> validate_password()
    |> validate_confirmation(:password)
    |> maybe_hash_password()
  end

  @doc """
  更新管理员信息的changeset
  """
  def update_changeset(admin, attrs) do
    admin
    |> cast(attrs, [:username, :is_root])
    |> validate_username()
    |> unique_constraint(:username, name: :admins_unique_username_index)
  end

  defp validate_username(changeset) do
    changeset
    |> validate_length(:username, min: 3, max: 20)
    |> validate_format(:username, ~r/^[a-zA-Z0-9_]+$/, message: "用户名只能包含字母、数字和下划线")
  end

  defp validate_password(changeset) do
    changeset
    |> validate_length(:password,
      min: 6,
      message: "密码长度必须至少为6个字符"
    )
  end

  defp validate_current_password(changeset, %{current_password: current_password}) do
    if valid_password?(changeset.data, current_password) do
      changeset
    else
      add_error(changeset, :current_password, "当前密码不正确")
    end
  end

  defp validate_current_password(changeset, _), do: changeset

  defp maybe_hash_password(changeset) do
    if password = get_change(changeset, :password) do
      # 使用bcrypt哈希密码
      changeset
      |> put_change(:hashed_password, Bcrypt.hash_pwd_salt(password))
      |> delete_change(:password)
      |> delete_change(:password_confirmation)
    else
      changeset
    end
  end

  @doc """
  验证密码是否正确
  """
  def valid_password?(%__MODULE__{hashed_password: hashed_password}, password)
      when is_binary(hashed_password) and is_binary(password) do
    Bcrypt.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _), do: false

  @doc """
  通过用户名查找管理员
  """
  def get_by_username(username) when is_binary(username) do
    Repo.get_by(__MODULE__, username: String.downcase(username))
  end

  @doc """
  获取所有管理员
  """
  def list_admins do
    from(a in __MODULE__, order_by: [desc: a.is_root, asc: a.username])
    |> Repo.all()
  end

  @doc """
  获取所有非根管理员
  """
  def list_regular_admins do
    from(a in __MODULE__, where: a.is_root == false, order_by: [asc: a.username])
    |> Repo.all()
  end

  @doc """
  获取根管理员
  """
  def get_root_admin do
    from(a in __MODULE__, where: a.is_root == true)
    |> Repo.one()
  end

  @doc """
  检查是否存在根管理员
  """
  def root_admin_exists? do
    from(a in __MODULE__, where: a.is_root == true)
    |> Repo.exists?()
  end

  @doc """
  管理员登录验证
  """
  def authenticate_admin(username, password) when is_binary(username) and is_binary(password) do
    admin = get_by_username(username)

    cond do
      admin && valid_password?(admin, password) ->
        {:ok, admin}

      admin ->
        {:error, "密码不正确"}

      true ->
        # 防止时序攻击，即使管理员不存在也执行相同的计算
        Bcrypt.no_user_verify()
        {:error, "管理员账号不存在"}
    end
  end

  @doc """
  创建管理员
  """
  def create_admin(attrs) do
    %__MODULE__{}
    |> create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  创建根管理员（只能在没有根管理员时创建）
  """
  def create_root_admin(attrs) do
    if root_admin_exists?() do
      {:error, "根管理员已存在"}
    else
      attrs = Map.put(attrs, :is_root, true)
      create_admin(attrs)
    end
  end

  @doc """
  更新管理员信息
  """
  def update_admin(admin, attrs) do
    admin
    |> update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  修改管理员密码
  """
  def update_password(admin, attrs) do
    admin
    |> password_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  重置管理员密码（由根管理员操作）
  """
  def reset_password(admin, attrs) do
    admin
    |> reset_password_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  删除管理员（不能删除根管理员）
  """
  def delete_admin(%__MODULE__{is_root: true}) do
    {:error, "不能删除根管理员"}
  end

  def delete_admin(admin) do
    Repo.delete(admin)
  end

  @doc """
  检查管理员权限
  """
  def has_permission?(%__MODULE__{is_root: true}, _permission), do: true
  def has_permission?(%__MODULE__{is_root: false}, :root_only), do: false
  def has_permission?(%__MODULE__{is_root: false}, _permission), do: true
  def has_permission?(_, _), do: false
end
