defmodule Cypridina.Accounts.AdminService do
  @moduledoc """
  管理员权限服务模块
  基于权限级别的简化权限系统：
  - 0: 普通用户
  - 1: 管理员
  - 2: 超级管理员
  """

  alias Cypridina.Accounts.User
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts

  # 权限级别常量
  @user_level 0
  @admin_level 1
  @super_admin_level 2

  @doc """
  检查用户是否为管理员（级别 >= 1）
  """
  def is_admin?(user) when is_struct(user, User) do
    user.permission_level >= @admin_level
  end

  def is_admin?(user_id) when is_binary(user_id) do
    # 暂时不支持通过ID查询，需要传入完整的用户结构体
    false
  end

  @doc """
  检查用户是否为超级管理员（级别 = 2）
  """
  def is_super_admin?(user) when is_struct(user, User) do
    user.permission_level == @super_admin_level
  end

  def is_super_admin?(user_id) when is_binary(user_id) do
    # 暂时不支持通过ID查询，需要传入完整的用户结构体
    false
  end

  @doc """
  检查用户权限级别是否满足要求
  """
  def has_permission_level?(user, required_level) when is_struct(user, User) do
    user.permission_level >= required_level
  end

  def has_permission_level?(user_id, _required_level) when is_binary(user_id) do
    # 暂时不支持通过ID查询，需要传入完整的用户结构体
    false
  end

  @doc """
  兼容性函数：检查用户是否有特定权限
  现在基于权限级别进行简化判断
  """
  def has_permission?(user, permission) when is_struct(user, User) do
    case permission do
      "super_admin" -> is_super_admin?(user)
      # 其他权限只要是管理员即可
      _ -> is_admin?(user)
    end
  end

  def has_permission?(user_id, _permission) when is_binary(user_id) do
    # 暂时不支持通过ID查询，需要传入完整的用户结构体
    false
  end

  @doc """
  创建管理员用户
  """
  def create_admin(attrs) do
    admin_attrs =
      Map.merge(attrs, %{
        permission_level: Map.get(attrs, :permission_level, @admin_level)
      })

    # 使用Ash资源直接创建
    Accounts.User
    |> Ash.Changeset.for_create(:register_with_username, admin_attrs)
    |> Ash.create()
  end

  @doc """
  更新用户的权限级别
  """
  def update_permission_level(user, permission_level) do
    user
    |> Ash.Changeset.for_update(:update_permission_level, %{permission_level: permission_level})
    |> Ash.update()
  end

  @doc """
  获取所有管理员用户
  """
  def list_admins do
    Accounts.User
    |> Ash.Query.for_read(:get_admins)
    |> Ash.read()
  end

  @doc """
  获取所有超级管理员用户
  """
  def list_super_admins do
    Accounts.User
    |> Ash.Query.for_read(:get_super_admins)
    |> Ash.read()
  end

  @doc """
  创建超级管理员（仅在系统初始化时使用）
  """
  def create_super_admin(attrs) do
    super_admin_attrs =
      Map.merge(attrs, %{
        permission_level: @super_admin_level
      })

    Accounts.User
    |> Ash.Changeset.for_create(:register_with_username, super_admin_attrs)
    |> Ash.create()
  end

  @doc """
  检查是否已存在超级管理员
  """
  def super_admin_exists? do
    case Accounts.User
         |> Ash.Query.for_read(:get_super_admins)
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, []} -> false
      {:ok, [_ | _]} -> true
      _ -> false
    end
  end

  @doc """
  权限级别检查装饰器函数
  """
  def require_permission_level(user, required_level, fun) when is_function(fun, 0) do
    if has_permission_level?(user, required_level) do
      fun.()
    else
      {:error, :insufficient_permissions}
    end
  end

  @doc """
  权限级别检查装饰器函数（带参数）
  """
  def require_permission_level(user, required_level, fun, args) when is_function(fun, 1) do
    if has_permission_level?(user, required_level) do
      fun.(args)
    else
      {:error, :insufficient_permissions}
    end
  end

  @doc """
  兼容性函数：权限检查装饰器
  """
  def require_permission(user, permission, fun) when is_function(fun, 0) do
    if has_permission?(user, permission) do
      fun.()
    else
      {:error, :insufficient_permissions}
    end
  end

  def require_permission(user, permission, fun, args) when is_function(fun, 1) do
    if has_permission?(user, permission) do
      fun.(args)
    else
      {:error, :insufficient_permissions}
    end
  end

  @doc """
  获取权限级别名称
  """
  def permission_level_name(level) do
    case level do
      2 -> "超级管理员"
      1 -> "管理员"
      0 -> "普通用户"
      _ -> "未知"
    end
  end

  @doc """
  获取所有权限级别
  """
  def permission_levels do
    [
      {@user_level, "普通用户"},
      {@admin_level, "管理员"},
      {@super_admin_level, "超级管理员"}
    ]
  end

  @doc """
  权限级别常量
  """
  def user_level, do: @user_level
  def admin_level, do: @admin_level
  def super_admin_level, do: @super_admin_level

  @doc """
  管理员操作日志记录
  """
  def log_admin_action(admin_user, action, target \\ nil, details \\ %{}) do
    log_data = %{
      admin_id: admin_user.id,
      admin_username: admin_user.username,
      permission_level: admin_user.permission_level,
      action: action,
      target: target,
      details: details,
      timestamp: DateTime.utc_now()
    }

    # 这里可以集成到日志系统或审计系统
    require Logger
    Logger.info("Admin Action: #{inspect(log_data)}")

    {:ok, log_data}
  end

  # 兼容性函数别名
  def is_root_admin?(user), do: is_super_admin?(user)
  def create_root_admin(attrs), do: create_super_admin(attrs)
  def root_admin_exists?(), do: super_admin_exists?()
  def list_root_admins(), do: list_super_admins()
end
