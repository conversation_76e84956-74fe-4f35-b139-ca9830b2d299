defmodule Cypridina.Accounts.EctoAdapterTest do
  @moduledoc """
  测试 Ecto 适配器的基本功能
  这个模块用于验证所有 Ecto 适配器是否正确配置
  """

  alias Cypridina.Ecto.Accounts.{
    EUser,
    EUserAsset,
    EAgentRelationship,
    ECommissionRecord,
    EAdmin,
    EToken,
    EUserIdentity
  }

  alias Cypridina.Repo

  @doc """
  测试所有 Ecto schema 的基本功能
  """
  def test_all_schemas do
    IO.puts("开始测试 Ecto 适配器...")

    test_user_schema()
    test_user_asset_schema()
    test_agent_relationship_schema()
    test_commission_record_schema()
    test_admin_schema()
    test_token_schema()
    test_user_identity_schema()

    IO.puts("所有 Ecto 适配器测试完成!")
  end

  defp test_user_schema do
    IO.puts("测试 EUser schema...")

    # 测试 changeset
    changeset =
      EUser.changeset(%EUser{}, %{
        numeric_id: 1001,
        username: "test_user",
        hashed_password: "hashed_password",
        is_agent: false,
        agent_level: 0
      })

    if changeset.valid? do
      IO.puts("✓ EUser changeset 验证通过")
    else
      IO.puts("✗ EUser changeset 验证失败: #{inspect(changeset.errors)}")
    end
  end

  defp test_user_asset_schema do
    IO.puts("测试 EUserAsset schema...")

    changeset =
      EUserAsset.changeset(%EUserAsset{}, %{
        user_id: Ecto.UUID.generate(),
        points: 1000
      })

    if changeset.valid? do
      IO.puts("✓ EUserAsset changeset 验证通过")
    else
      IO.puts("✗ EUserAsset changeset 验证失败: #{inspect(changeset.errors)}")
    end
  end

  defp test_agent_relationship_schema do
    IO.puts("测试 EAgentRelationship schema...")

    changeset =
      EAgentRelationship.changeset(%EAgentRelationship{}, %{
        agent_id: Ecto.UUID.generate(),
        subordinate_id: Ecto.UUID.generate(),
        level: 1,
        commission_rate: Decimal.new("0.05"),
        status: 1
      })

    if changeset.valid? do
      IO.puts("✓ EAgentRelationship changeset 验证通过")
    else
      IO.puts("✗ EAgentRelationship changeset 验证失败: #{inspect(changeset.errors)}")
    end
  end

  defp test_commission_record_schema do
    IO.puts("测试 ECommissionRecord schema...")

    changeset =
      ECommissionRecord.changeset(%ECommissionRecord{}, %{
        agent_id: Ecto.UUID.generate(),
        subordinate_id: Ecto.UUID.generate(),
        transaction_type: "bet",
        transaction_id: Ecto.UUID.generate(),
        original_amount: Decimal.new("100"),
        commission_rate: Decimal.new("0.05"),
        commission_amount: Decimal.new("5"),
        status: 1
      })

    if changeset.valid? do
      IO.puts("✓ ECommissionRecord changeset 验证通过")
    else
      IO.puts("✗ ECommissionRecord changeset 验证失败: #{inspect(changeset.errors)}")
    end
  end

  defp test_admin_schema do
    IO.puts("测试 EAdmin schema...")

    changeset =
      EAdmin.changeset(%EAdmin{}, %{
        username: "admin_user",
        hashed_password: "hashed_password",
        is_root: false
      })

    if changeset.valid? do
      IO.puts("✓ EAdmin changeset 验证通过")
    else
      IO.puts("✗ EAdmin changeset 验证失败: #{inspect(changeset.errors)}")
    end
  end

  defp test_token_schema do
    IO.puts("测试 EToken schema...")

    changeset =
      EToken.changeset(%EToken{}, %{
        jti: "test_jti_#{:rand.uniform(10000)}",
        subject: "test_subject",
        expires_at: DateTime.add(DateTime.utc_now(), 3600),
        purpose: "sign_in",
        extra_data: %{}
      })

    if changeset.valid? do
      IO.puts("✓ EToken changeset 验证通过")
    else
      IO.puts("✗ EToken changeset 验证失败: #{inspect(changeset.errors)}")
    end
  end

  defp test_user_identity_schema do
    IO.puts("测试 EUserIdentity schema...")

    changeset =
      EUserIdentity.changeset(%EUserIdentity{}, %{
        user_id: Ecto.UUID.generate(),
        strategy: "oauth2",
        uid: "test_uid_#{:rand.uniform(10000)}",
        access_token: "test_access_token",
        expires_at: DateTime.add(DateTime.utc_now(), 3600)
      })

    if changeset.valid? do
      IO.puts("✓ EUserIdentity changeset 验证通过")
    else
      IO.puts("✗ EUserIdentity changeset 验证失败: #{inspect(changeset.errors)}")
    end
  end

  @doc """
  测试关联关系
  """
  def test_associations do
    IO.puts("测试关联关系...")

    # 测试 User 的关联
    import Ecto.Query
    _user_query = from(u in EUser, preload: [:asset, :agent_relationships])
    IO.puts("✓ User 关联查询构建成功")

    # 测试 UserAsset 的关联
    _asset_query = from(a in EUserAsset, preload: [:user])
    IO.puts("✓ UserAsset 关联查询构建成功")

    # 测试 AgentRelationship 的关联
    _relationship_query = from(r in EAgentRelationship, preload: [:agent, :subordinate])
    IO.puts("✓ AgentRelationship 关联查询构建成功")

    IO.puts("关联关系测试完成!")
  end

  @doc """
  运行完整测试
  """
  def run_full_test do
    test_all_schemas()
    test_associations()
    IO.puts("\n🎉 所有测试完成!")
  end
end
