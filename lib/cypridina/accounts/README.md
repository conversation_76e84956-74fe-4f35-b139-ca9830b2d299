# Cypridina

To start your Phoenix server:

* Run `mix setup` to install and setup dependencies
* Start Phoenix endpoint with `mix phx.server` or inside IEx with `iex -S mix phx.server`

Now you can visit [`localhost:4000`](http://localhost:4000) from your browser.

Ready to run in production? Please [check our deployment guides](https://hexdocs.pm/phoenix/deployment.html).

## Learn more

* Official website: https://www.phoenixframework.org/
* Guides: https://hexdocs.pm/phoenix/overview.html
* Docs: https://hexdocs.pm/phoenix
* Forum: https://elixirforum.com/c/phoenix-forum
* Source: https://github.com/phoenixframework/phoenix

####设计用户系统
请帮我为此项目设计一个通用的用户系统
1. 使用ash framework 3.0和phoenix帮我设计用户的数据模型
2. 第一步，实现注册账号密码登录
3. 第二部，实现邮箱密码登录
4. 第三步，实现微信登录
5. 第四步，实现手机验证码登录
6. 请帮我实现一个管理员登录系统、管理员可以通过设定账户和密码直接创建新用户

数据模型用mix ash.gen.resource生成，参照如下例子:
```bash

mix ash.gen.resource Cypridina.Accounts.User \
--uuid-primary-key id \
--attribute username:string \
--attribute email:string \
--attribute hashed_password:string \
--attribute phone:string \
--attribute nickname:string \
--attribute avatar:string \
--attribute status:integer \
--attribute last_login_at:utc_datetime_usec \
--attribute last_login_ip:string \
--timestamps \
--extend postgres

mix ash.gen.resource Cypridina.Accounts.UserToken \
--uuid-primary-key id \
--attribute token:string \
--attribute context:string \
--attribute sent_to:string \
--attribute expires_at:utc_datetime_usec \
--timestamps \
--extend postgres