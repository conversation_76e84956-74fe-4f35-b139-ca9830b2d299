defmodule Cypridina.Utils.DateTimeUtils do
  @moduledoc """
  提供日期和时间处理相关的工具函数
  """

  @doc """
  格式化日期时间为友好的显示格式

  ## 示例

      iex> Cypridina.Utils.DateTimeUtils.format_datetime(~U[2023-01-01 10:00:00Z])
      "2023年01月01日 10:00"
  """
  def format_datetime(datetime) do
    %{year: year, month: month, day: day, hour: hour, minute: minute} = datetime

    "#{year}年#{String.pad_leading("#{month}", 2, "0")}月#{String.pad_leading("#{day}", 2, "0")}日 #{String.pad_leading("#{hour}", 2, "0")}:#{String.pad_leading("#{minute}", 2, "0")}"
  end

  @doc """
  计算两个日期时间之间的差异（以秒为单位）

  ## 示例

      iex> Cypridina.Utils.DateTimeUtils.diff_seconds(~U[2023-01-01 10:30:00Z], ~U[2023-01-01 10:00:00Z])
      1800
  """
  def diff_seconds(datetime1, datetime2) do
    DateTime.diff(datetime1, datetime2, :second)
  end

  @doc """
  获取当前时间的 UNIX 时间戳（秒）

  ## 示例

      iex> timestamp = Cypridina.Utils.DateTimeUtils.current_timestamp()
      iex> is_integer(timestamp)
      true
  """
  def current_timestamp do
    DateTime.utc_now() |> DateTime.to_unix()
  end

  @doc """
  将时间戳转换为 DateTime 结构

  ## 示例

      iex> Cypridina.Utils.DateTimeUtils.timestamp_to_datetime(1609502400)
      ~U[2021-01-01 10:00:00Z]
  """
  def timestamp_to_datetime(timestamp) when is_integer(timestamp) do
    DateTime.from_unix!(timestamp)
  end
end
