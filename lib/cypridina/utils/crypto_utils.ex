defmodule Cypridina.Utils.CryptoUtils do
  import Bitwise

  @doc """
  将字符串或二进制数据转换为十六进制表示.
  如果输入是字符串, 它会被视为其UTF-8字节序列进行转换.
  如果输入是任意二进制数据, 它的每个字节都会被转换.

  此实现通过直接处理字节来避免 `UnicodeConversionError`,
  并与JavaScript `convertToHex` 在ASCII范围内的行为一致.
  对于超出ASCII范围的字符, 此函数转换其UTF-8字节,
  而JavaScript的 `charCodeAt().toString(16)` 处理UTF-16代码单元.

  ## 示例

      iex> Cypridina.Utils.CryptoUtils.convert_to_hex("hello world")
      "68656c6c6f20776f726c64"
      iex> Cypridina.Utils.CryptoUtils.convert_to_hex(<<255, 0, 10>>)
      "ff000a"
      iex> Cypridina.Utils.CryptoUtils.convert_to_hex("你好") # Hex of UTF-8 bytes
      "e4bda0e5a5bd"
      iex> Cypridina.Utils.CryptoUtils.convert_to_hex(nil)
      ""
      iex> Cypridina.Utils.CryptoUtils.convert_to_hex("")
      ""
  """
  def convert_to_hex(data) do
    input_binary =
      case data do
        nil ->
          <<>>

        _ when is_binary(data) ->
          data

        _ ->
          # 将非二进制类型转换为字符串 (Elixir中字符串是UTF-8二进制)
          to_string(data)
      end

    # 迭代二进制中的每个字节并将其转换为十六进制表示
    for <<byte <- input_binary>>, into: "" do
      hex = Integer.to_string(byte, 16)
      # 如果十六进制表示是单个数字，则在其前面填充一个零
      if String.length(hex) == 1, do: "0" <> hex, else: hex
    end
  end

  @doc """
  将十六进制字符串转换回原始二进制数据

  与JavaScript版本的convertFromHex函数保持一致

  ## 示例

      iex> Cypridina.Utils.CryptoUtils.convert_from_hex("68656c6c6f20776f726c64")
      "hello world"
      iex> Cypridina.Utils.CryptoUtils.convert_from_hex("e4bda0e5a5bd")
      "你好"
      iex> Cypridina.Utils.CryptoUtils.convert_from_hex("")
      ""
      iex> Cypridina.Utils.CryptoUtils.convert_from_hex(nil)
      ""
  """
  def convert_from_hex(hex_str) do
    hex_str = if hex_str, do: hex_str, else: ""

    # 按照JavaScript版本的逻辑，每2个字符一组进行处理
    hex_str
    |> String.graphemes()
    # 丢弃不完整的对
    |> Enum.chunk_every(2, 2, :discard)
    |> Enum.map(fn [h1, h2] ->
      hex_pair = h1 <> h2

      case Integer.parse(hex_pair, 16) do
        {code, _} -> <<code::utf8>>
        # 或者可以抛出错误，取决于期望的行为
        :error -> ""
      end
    end)
    |> Enum.join("")
  end

  @moduledoc """
  提供加密解密相关的工具函数，包括 XXTEA 算法实现
  """

  @doc """
  使用 XXTEA 算法加密数据，与 JavaScript 版本保持一致

  ## 参数
    - data: 待加密的二进制数据或字符串
    - key: 密钥

  ## 返回
    - 加密后的二进制数据

  ## 示例

      iex> key = "0123456789ABCDEF"
      iex> Cypridina.Utils.CryptoUtils.encrypt_xxtea("Hello, World!", key)
      <<...>> # 加密后的二进制数据
  """
  def encrypt_xxtea(data, key) when is_binary(data) and is_binary(key) do
    if data == "" do
      ""
    else
      # 标准化密钥至少16字节
      normalized_key = normalize_key_16(key)

      # 将输入转换为32位整数数组，并添加长度信息
      v = str_to_longs(data, true)
      k = str_to_longs(normalized_key, false)

      # 确保密钥长度至少为4个整数
      k = if length(k) < 4, do: k ++ List.duplicate(0, 4 - length(k)), else: Enum.take(k, 4)

      # 执行XXTEA加密
      encrypted = encrypt_longs(v, k)

      # 将结果转换回二进制
      longs_to_str(encrypted, false)
    end
  end

  @doc """
  使用 XXTEA 算法解密数据，与 JavaScript 版本保持一致

  ## 参数
    - data: 待解密的二进制数据
    - key: 密钥

  ## 返回
    - 解密后的二进制数据

  ## 示例

      iex> key = "0123456789ABCDEF"
      iex> encrypted = Cypridina.Utils.CryptoUtils.encrypt_xxtea("Hello, World!", key)
      iex> Cypridina.Utils.CryptoUtils.decrypt_xxtea(encrypted, key)
      "Hello, World!"
  """
  def decrypt_xxtea(data, key) when is_binary(data) and is_binary(key) do
    if data == "" do
      ""
    else
      # 标准化密钥至少16字节
      normalized_key = normalize_key_16(key)

      # 将输入转换为32位整数数组
      v = str_to_longs(data, false)
      k = str_to_longs(normalized_key, false)

      # 确保密钥长度至少为4个整数
      k = if length(k) < 4, do: k ++ List.duplicate(0, 4 - length(k)), else: Enum.take(k, 4)

      # 执行XXTEA解密
      decrypted = decrypt_longs(v, k)

      # 将结果转换回字符串，并使用长度信息截取原始数据
      longs_to_str(decrypted, true)
    end
  end

  # XXTEA加密整数数组
  defp encrypt_longs(v, k) when is_list(v) and is_list(k) do
    n = length(v)

    # 至少有两个整数时才能进行加密
    if n <= 1 do
      v
    else
      z = Enum.at(v, n - 1)
      _y = Enum.at(v, 0)
      delta = 0x9E3779B9
      # 通常为6 + div(52, n)
      q = 6 + div(52, n)
      sum = 0

      # 主循环
      encrypt_rounds(v, k, n, z, delta, q, sum)
    end
  end

  # XXTEA解密整数数组
  defp decrypt_longs(v, k) when is_list(v) and is_list(k) do
    n = length(v)

    # 至少有两个整数时才能进行解密
    if n <= 1 do
      v
    else
      _z = Enum.at(v, n - 1)
      y = Enum.at(v, 0)
      delta = 0x9E3779B9
      # 通常为6 + div(52, n)
      q = 6 + div(52, n)
      sum = delta * q

      # 主循环
      decrypt_rounds(v, k, n, y, delta, sum)
    end
  end

  # 加密迭代
  defp encrypt_rounds(v, _k, _n, _z, _delta, 0, _sum), do: v

  defp encrypt_rounds(v, k, n, z, delta, q, sum) do
    # 更新sum
    new_sum = sum + delta &&& 0xFFFFFFFF
    e = new_sum >>> 2 &&& 3

    # 更新数组元素
    {new_v, new_z} =
      Enum.reduce(0..(n - 1), {v, z}, fn i, {acc_v, _} ->
        y = Enum.at(acc_v, rem(i + 1, n))
        z = Enum.at(acc_v, i)

        # 计算mx
        mx = mx_compute(e, i, new_sum, y, z, k)

        # 更新当前元素
        new_v = List.update_at(acc_v, i, fn val -> val + mx &&& 0xFFFFFFFF end)

        {new_v, Enum.at(new_v, i)}
      end)

    # 递归下一轮
    encrypt_rounds(new_v, k, n, new_z, delta, q - 1, new_sum)
  end

  # 解密迭代
  defp decrypt_rounds(v, _k, _n, _y, _delta, 0), do: v

  defp decrypt_rounds(v, k, n, y, delta, sum) do
    e = sum >>> 2 &&& 3

    # 从后向前更新数组元素
    {new_v, new_y} =
      Enum.reduce((n - 1)..0//-1, {v, y}, fn i, {acc_v, _} ->
        z = Enum.at(acc_v, if(i > 0, do: i - 1, else: n - 1))
        y = Enum.at(acc_v, rem(i + 1, n))

        # 计算mx
        mx = mx_compute(e, i, sum, y, z, k)

        # 更新当前元素
        new_v = List.update_at(acc_v, i, fn val -> val - mx &&& 0xFFFFFFFF end)

        {new_v, Enum.at(new_v, i)}
      end)

    # 递归下一轮
    new_sum = sum - delta &&& 0xFFFFFFFF
    decrypt_rounds(new_v, k, n, new_y, delta, new_sum)
  end

  # 计算mx (XXTEA核心操作)
  defp mx_compute(e, p, sum, y, z, k) do
    # 根据原始算法中的公式
    sum_term = bxor(z >>> 5, y <<< 2)
    y_z_term = bxor(y >>> 3, z <<< 4)

    # 密钥索引
    p_mod_4 = rem(p, 4)
    key_idx = bxor(p_mod_4, e)
    key_val = Enum.at(k, key_idx)

    # 计算最终的mx值
    result = bxor(sum_term + y_z_term, bxor(sum, key_val) + bxor(z, y))
    result &&& 0xFFFFFFFF
  end

  # 将字符串转换为32位整数列表
  defp str_to_longs(str, include_length) do
    n = byte_size(str)

    # 计算需要多少个整数（每4字节一个整数）
    m = if rem(n, 4) > 0, do: div(n, 4) + 1, else: div(n, 4)
    result = List.duplicate(0, m)

    # 填充整数列表
    result =
      Enum.reduce(0..(n - 1), result, fn i, acc ->
        # 计算当前字节的位置和偏移
        j = div(i, 4)
        # 获取当前字节的值
        <<_::binary-size(i), byte, _::binary>> = str <> <<0>>

        # 根据偏移更新整数
        List.update_at(acc, j, fn val ->
          val ||| byte <<< (8 * rem(i, 4))
        end)
      end)

    # 如果需要，添加长度信息
    if include_length && n > 0 do
      result ++ [n]
    else
      result
    end
  end

  # 将32位整数列表转换回字符串
  defp longs_to_str(v, include_length) do
    n = length(v)

    # 如果列表为空，返回空字符串
    if n == 0 do
      ""
    else
      # 如果包含长度信息，获取并验证
      result_length =
        if include_length do
          # 获取存储的长度
          stored_len = Enum.at(v, n - 1)

          # 预计长度根据数组大小计算
          expected_len = (n - 1) * 4

          # 验证长度有效性
          if stored_len > 0 && stored_len <= expected_len do
            stored_len
          else
            expected_len
          end
        else
          n * 4
        end

      # 将整数转换为字节
      bytes =
        v
        |> Enum.take(if(include_length, do: n - 1, else: n))
        |> Enum.flat_map(fn int ->
          [
            int &&& 0xFF,
            int >>> 8 &&& 0xFF,
            int >>> 16 &&& 0xFF,
            int >>> 24 &&& 0xFF
          ]
        end)
        # 只取所需长度
        |> Enum.take(result_length)

      # 将字节列表转换为二进制
      for byte <- bytes, into: <<>>, do: <<byte>>
    end
  end

  # 规范化密钥为16字节
  defp normalize_key_16(key) do
    key_size = byte_size(key)

    cond do
      key_size == 16 -> key
      key_size < 16 -> key <> :binary.copy(<<0>>, 16 - key_size)
      key_size > 16 -> binary_part(key, 0, 16)
    end
  end

  # 辅助函数

  # 对应JavaScript中的str2long函数
  # 将二进制数据转换为32位整数列表
  defp str2long(str, include_length) do
    byte_size_str = byte_size(str)
    result = []

    # 每4个字节组成一个32位整数
    result =
      for i <- 0..(div(byte_size_str, 4) - 1), into: [] do
        idx = i * 4
        # 获取4个字节
        <<_::binary-size(idx), b0, b1, b2, b3, _::binary>> = str <> <<0, 0, 0>>
        # 组合为一个32位整数(小端序)
        b0 ||| b1 <<< 8 ||| b2 <<< 16 ||| b3 <<< 24
      end

    # 处理剩余的字节（不足4个字节）
    remainder = rem(byte_size_str, 4)

    result =
      if remainder > 0 do
        # 获取剩余字节
        start_idx = byte_size_str - remainder
        <<_::binary-size(start_idx), remaining::binary>> = str
        # 填充后转换为32位整数
        last_int =
          case remaining do
            <<b0>> ->
              b0

            <<b0, b1>> ->
              b0 ||| b1 <<< 8

            <<b0, b1, b2>> ->
              b0 ||| b1 <<< 8 ||| b2 <<< 16

            _ ->
              0
          end

        result ++ [last_int]
      else
        result
      end

    # 如果需要附加长度信息
    if include_length do
      result ++ [byte_size_str]
    else
      result
    end
  end

  # 对应JavaScript中的long2str函数
  # 将32位整数列表转换回二进制字符串
  defp long2str(v, include_length) do
    vl = length(v)

    # 如果v为空，直接返回空字符串
    if vl == 0 do
      ""
    else
      n = (vl - 1) * 4

      # 如果使用了长度信息，获取实际长度
      actual_length =
        if include_length do
          m = Enum.at(v, vl - 1)
          # 确认长度信息是否有效
          if m < n - 3 || m > n do
            # 无效长度，使用默认长度
            n
          else
            # 使用指定的实际长度
            m
          end
        else
          # 不需要处理长度信息，使用全部数据
          vl * 4
        end

      # 生成字节序列
      result =
        v
        # 如果include_length为true，忽略最后一个整数（长度信息）
        |> Enum.take(if include_length, do: vl - 1, else: vl)
        |> Enum.map(fn int ->
          <<
            int &&& 0xFF,
            int >>> 8 &&& 0xFF,
            int >>> 16 &&& 0xFF,
            int >>> 24 &&& 0xFF
          >>
        end)
        |> Enum.join()

      # 截取到实际长度
      if byte_size(result) >= actual_length do
        binary_part(result, 0, actual_length)
      else
        result
      end
    end
  end

  # 规范化密钥为16字节
  defp normalize_key(key) do
    key_size = byte_size(key)

    cond do
      key_size == 16 -> key
      key_size < 16 -> key <> :binary.copy(<<0>>, 16 - key_size)
      true -> binary_part(key, 0, 16)
    end
  end

  @doc """
  使用 Base64 编码二进制数据

  @doc \"""
  解码 Base64 编码的字符串

  ## 示例

      iex> Cypridina.Utils.CryptoUtils.base64_decode("SGVsbG8=")
      {:ok, "Hello"}
  """
  def base64_decode(encoded) when is_binary(encoded) do
    Base.decode64(encoded)
  end

  @doc """
  使用 AES-256-CBC 算法加密数据

  ## 参数
    - data: 待加密的二进制数据或字符串
    - key: 密钥，长度必须为32字节 (256位)
    - iv: 初始化向量，长度必须为16字节，如果未提供则自动生成

  ## 返回
    - {iv, 加密后的二进制数据}

  ## 示例

      iex> key = :crypto.strong_rand_bytes(32)
      iex> {iv, ciphertext} = Cypridina.Utils.CryptoUtils.encrypt_aes_256_cbc("秘密消息", key)
  """
  def encrypt_aes_256_cbc(data, key, iv \\ nil) when is_binary(data) and is_binary(key) do
    # 确保密钥长度为32字节 (256位)
    key = normalize_aes_key(key, 32)

    # 如果未提供IV，则生成一个随机IV
    iv = if is_nil(iv), do: :crypto.strong_rand_bytes(16), else: normalize_iv(iv)

    # 使用PKCS7填充
    padded_data = pkcs7_pad(data, 16)

    # 执行AES-256-CBC加密
    ciphertext = :crypto.crypto_one_time(:aes_256_cbc, key, iv, padded_data, true)

    {iv, ciphertext}
  end

  @doc """
  使用 AES-256-CBC 算法解密数据

  ## 参数
    - ciphertext: 待解密的二进制数据
    - key: 密钥，长度必须为32字节 (256位)
    - iv: 初始化向量，长度必须为16字节

  ## 返回
    - 解密后的二进制数据

  ## 示例

      iex> key = :crypto.strong_rand_bytes(32)
      iex> {iv, ciphertext} = Cypridina.Utils.CryptoUtils.encrypt_aes_256_cbc("秘密消息", key)
      iex> Cypridina.Utils.CryptoUtils.decrypt_aes_256_cbc(ciphertext, key, iv)
      "秘密消息"
  """
  def decrypt_aes_256_cbc(ciphertext, key, iv)
      when is_binary(ciphertext) and is_binary(key) and is_binary(iv) do
    # 确保密钥长度为32字节 (256位)
    key = normalize_aes_key(key, 32)

    # 确保IV长度为16字节
    iv = normalize_iv(iv)

    # 执行AES-256-CBC解密
    decrypted = :crypto.crypto_one_time(:aes_256_cbc, key, iv, ciphertext, false)

    # 去除PKCS7填充
    pkcs7_unpad(decrypted)
  end

  # 确保AES密钥长度正确
  defp normalize_aes_key(key, required_length) do
    key_size = byte_size(key)

    cond do
      key_size == required_length ->
        key

      key_size < required_length ->
        # 对于短密钥，使用SHA-256填充到所需长度
        hash = :crypto.hash(:sha256, key)
        if required_length == 32, do: hash, else: binary_part(hash, 0, required_length)

      true ->
        binary_part(key, 0, required_length)
    end
  end

  # 确保IV长度为16字节
  defp normalize_iv(iv) do
    iv_size = byte_size(iv)

    cond do
      iv_size == 16 -> iv
      iv_size < 16 -> iv <> :binary.copy(<<0>>, 16 - iv_size)
      true -> binary_part(iv, 0, 16)
    end
  end

  # PKCS7填充
  defp pkcs7_pad(data, block_size) do
    pad_length = block_size - rem(byte_size(data), block_size)
    data <> :binary.copy(<<pad_length>>, pad_length)
  end

  # PKCS7去填充
  defp pkcs7_unpad(data) do
    pad_length = :binary.last(data)
    binary_part(data, 0, byte_size(data) - pad_length)
  end
end
