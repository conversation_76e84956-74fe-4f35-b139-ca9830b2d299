defmodule CypridinaWeb.Live.AdminDashboardLive do
  @moduledoc """
  统一的管理后台首页

  根据用户权限等级显示不同的内容：
  - 超级管理员：可以看到所有功能和数据
  - 管理员：可以看到大部分功能，但不能管理其他管理员
  - 根代理：可以看到代理相关功能和自己的下级数据
  - 代理：只能看到自己的下级数据和基本功能
  """

  use CypridinaWeb, :live_view

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.{User, AgentService}
  require Ash.Query

  def mount(_params, _session, socket) do
    # 权限已经在 on_mount 中检查，这里可以直接使用
    user = socket.assigns.current_user
    role = socket.assigns.user_role

    # 获取统计数据
    stats = get_dashboard_stats(user, role)

    socket =
      socket
      |> assign(:stats, stats)
      |> assign(:page_title, "管理后台")

    {:ok, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="admin-dashboard">
      <!-- 页面标题和用户信息 -->
      <div class="dashboard-header">
        <h1 class="text-3xl font-bold text-gray-900">管理后台</h1>
        <div class="user-info">
          <span class="text-sm text-gray-600">
            欢迎，<strong><%= @current_user.username %></strong> ({@permission_level_name})
          </span>
        </div>
      </div>
      
    <!-- 统计卡片 -->
      <div class="stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 用户统计 -->
        <div class="stat-card bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">
                <%= if @can_view_all_data do %>
                  总用户数
                <% else %>
                  我的下级用户
                <% end %>
              </p>
              <p class="text-2xl font-semibold text-gray-900">{@stats.user_count}</p>
            </div>
          </div>
        </div>
        
    <!-- 代理统计 - 只有管理员和代理能看到 -->
        <div
          :if={@user_role in [:super_admin, :admin, :root_agent, :agent]}
          class="stat-card bg-white p-6 rounded-lg shadow"
        >
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">
                <%= if @can_view_all_data do %>
                  总代理数
                <% else %>
                  我的下级代理
                <% end %>
              </p>
              <p class="text-2xl font-semibold text-gray-900">{@stats.agent_count}</p>
            </div>
          </div>
        </div>
        
    <!-- 佣金统计 - 代理和管理员能看到 -->
        <div
          :if={@user_role in [:super_admin, :admin, :root_agent, :agent]}
          class="stat-card bg-white p-6 rounded-lg shadow"
        >
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">本月佣金</p>
              <p class="text-2xl font-semibold text-gray-900">¥{@stats.commission_amount}</p>
            </div>
          </div>
        </div>
        
    <!-- 在线用户统计 -->
        <div class="stat-card bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">在线用户</p>
              <p class="text-2xl font-semibold text-gray-900">{@stats.online_users}</p>
            </div>
          </div>
        </div>
      </div>
      
    <!-- 快捷操作 -->
      <div class="quick-actions mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">快捷操作</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          
    <!-- 用户管理 -->
          <.link
            navigate="/admin/users"
            class="action-card bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">用户管理</p>
                <p class="text-xs text-gray-500">
                  <%= if @can_view_all_data do %>
                    管理所有用户
                  <% else %>
                    管理我的下级用户
                  <% end %>
                </p>
              </div>
            </div>
          </.link>
          
    <!-- 代理管理 - 只有管理员和代理能看到 -->
          <.link
            :if={@user_role in [:super_admin, :admin, :root_agent, :agent]}
            navigate="/admin/agent_relationships"
            class="action-card bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">代理管理</p>
                <p class="text-xs text-gray-500">
                  <%= if @can_view_all_data do %>
                    管理所有代理关系
                  <% else %>
                    管理我的下级代理
                  <% end %>
                </p>
              </div>
            </div>
          </.link>
          
    <!-- 创建用户 -->
          <.link
            navigate="/admin/create_user"
            class="action-card bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">创建用户</p>
                <p class="text-xs text-gray-500">添加新用户</p>
              </div>
            </div>
          </.link>
          
    <!-- 佣金记录 - 代理和管理员能看到 -->
          <.link
            :if={@user_role in [:super_admin, :admin, :root_agent, :agent]}
            navigate="/admin/commission_records"
            class="action-card bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">佣金记录</p>
                <p class="text-xs text-gray-500">查看佣金明细</p>
              </div>
            </div>
          </.link>
          
    <!-- 比赛控制 -->
          <.link
            navigate="/admin/race_control"
            class="action-card bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">比赛控制</p>
                <p class="text-xs text-gray-500">管理比赛设置</p>
              </div>
            </div>
          </.link>
          
    <!-- 系统设置 - 只有超级管理员能看到 -->
          <.link
            :if={@user_role == :super_admin}
            navigate="/admin/settings"
            class="action-card bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">系统设置</p>
                <p class="text-xs text-gray-500">系统配置管理</p>
              </div>
            </div>
          </.link>
        </div>
      </div>
      
    <!-- 权限说明 -->
      <div class="permissions-info bg-gray-50 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-900 mb-2">当前权限说明</h3>
        <div class="text-xs text-gray-600 space-y-1">
          <p><strong>您的角色：</strong>{@permission_level_name}</p>
          <div class="flex flex-wrap gap-2 mt-2">
            <span
              :if={@can_manage_users}
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              用户管理
            </span>
            <span
              :if={@can_manage_agents}
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
            >
              代理管理
            </span>
            <span
              :if={@can_view_all_data}
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
            >
              全局数据查看
            </span>
            <span
              :if={@user_role in [:root_agent, :agent]}
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
            >
              代理功能
            </span>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # 根据用户权限获取不同的统计数据
  defp get_dashboard_stats(user, role) do
    case role do
      role when role in [:super_admin, :admin] ->
        # 管理员可以看到全局统计
        %{
          user_count: get_total_user_count(),
          agent_count: get_total_agent_count(),
          commission_amount: get_total_commission_amount(),
          online_users: get_online_user_count()
        }

      role when role in [:root_agent, :agent] ->
        # 代理只能看到自己下级的统计
        %{
          user_count: get_subordinate_user_count(user),
          agent_count: get_subordinate_agent_count(user),
          commission_amount: get_user_commission_amount(user),
          online_users: get_online_user_count()
        }

      _ ->
        # 默认统计
        %{
          user_count: 0,
          agent_count: 0,
          commission_amount: 0,
          online_users: get_online_user_count()
        }
    end
  end

  # 获取总用户数
  defp get_total_user_count do
    case User |> Ash.count() do
      {:ok, count} -> count
      _ -> 0
    end
  end

  # 获取总代理数
  defp get_total_agent_count do
    case User |> Ash.Query.filter(agent_level: [gte: 0]) |> Ash.count() do
      {:ok, count} -> count
      _ -> 0
    end
  end

  # 获取下级用户数
  defp get_subordinate_user_count(user) do
    # 这里需要根据实际的代理关系逻辑来实现
    # 暂时返回模拟数据
    case AgentService.get_subordinate_count(user) do
      {:ok, count} -> count
      _ -> 0
    end
  end

  # 获取下级代理数
  defp get_subordinate_agent_count(user) do
    # 这里需要根据实际的代理关系逻辑来实现
    # 暂时返回模拟数据
    case AgentService.get_subordinate_agent_count(user) do
      {:ok, count} -> count
      _ -> 0
    end
  end

  # 获取总佣金金额
  defp get_total_commission_amount do
    # 这里需要根据实际的佣金记录来实现
    # 暂时返回模拟数据
    12580.50
  end

  # 获取用户佣金金额
  defp get_user_commission_amount(user) do
    # 这里需要根据实际的佣金记录来实现
    # 暂时返回模拟数据
    case AgentService.get_user_commission(user) do
      {:ok, amount} -> amount
      _ -> 0.0
    end
  end

  # 获取在线用户数
  defp get_online_user_count do
    # 这里需要根据实际的在线用户统计来实现
    # 暂时返回模拟数据
    42
  end
end
