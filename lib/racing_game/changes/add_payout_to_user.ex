defmodule Cypridina.RacingGame.Changes.AddPayoutToUser do
  use Ash.Resource.Change

  def change(changeset, _opts, _context) do
    bet = Ash.Changeset.data(changeset)
    status = Ash.Changeset.get_attribute(changeset, :status)

    if status == :win do
      payout_amount = Ash.Changeset.get_attribute(changeset, :payout_amount)

      # 使用新的用户资产接口添加积分
      case Cypridina.Accounts.add_points(bet.user_id, payout_amount) do
        {:ok, _asset} ->
          changeset

        {:error, error} ->
          Ash.Changeset.add_error(changeset,
            field: :payout_amount,
            message: "增加积分失败: #{inspect(error)}"
          )
      end
    else
      changeset
    end
  end
end
