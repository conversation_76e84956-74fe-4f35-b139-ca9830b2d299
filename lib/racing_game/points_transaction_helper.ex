defmodule Cypridina.RacingGame.PointsTransactionHelper do
  @moduledoc """
  积分变动记录的辅助模块
  提供各种积分变动场景的记录功能
  """

  require Logger
  alias Cypridina.RacingGame.PointsTransaction

  @doc """
  记录转账收入的积分变动
  """
  def record_transfer_in(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      from_user_id: Keyword.get(opts, :from_user_id),
      from_username: Keyword.get(opts, :from_username),
      transfer_amount: amount,
      reason: Keyword.get(opts, :reason)
    }

    description = build_transfer_description(:in, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :transfer_in,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录转账支出的积分变动
  """
  def record_transfer_out(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      to_user_id: Keyword.get(opts, :to_user_id),
      to_username: Keyword.get(opts, :to_username),
      transfer_amount: abs(amount),
      reason: Keyword.get(opts, :reason)
    }

    description = build_transfer_description(:out, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :transfer_out,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录管理员增加积分的变动
  """
  def record_admin_add(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      admin_id: Keyword.get(opts, :admin_id),
      admin_username: Keyword.get(opts, :admin_username),
      add_amount: amount,
      reason: Keyword.get(opts, :reason),
      operation_type: "admin_add"
    }

    description = build_admin_description(:add, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :admin_add,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录管理员减少积分的变动
  """
  def record_admin_subtract(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      admin_id: Keyword.get(opts, :admin_id),
      admin_username: Keyword.get(opts, :admin_username),
      subtract_amount: abs(amount),
      reason: Keyword.get(opts, :reason),
      operation_type: "admin_subtract"
    }

    description = build_admin_description(:subtract, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :admin_subtract,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录手动增加积分的变动
  """
  def record_manual_add(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      add_amount: amount,
      reason: Keyword.get(opts, :reason, "手动增加积分"),
      operation_type: "manual_add"
    }

    description = Keyword.get(opts, :reason, "手动增加积分")

    create_transaction(%{
      user_id: user_id,
      transaction_type: :manual_add,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录手动减少积分的变动
  """
  def record_manual_subtract(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      subtract_amount: abs(amount),
      reason: Keyword.get(opts, :reason, "手动减少积分"),
      operation_type: "manual_subtract"
    }

    description = Keyword.get(opts, :reason, "手动减少积分")

    create_transaction(%{
      user_id: user_id,
      transaction_type: :manual_subtract,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  # 私有函数：创建交易记录
  defp create_transaction(attrs) do
    case PointsTransaction.create_transaction(attrs) do
      {:ok, transaction} ->
        Logger.info("积分变动记录创建成功: #{inspect(transaction)}")
        {:ok, transaction}

      {:error, reason} ->
        Logger.error("积分变动记录创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 私有函数：构建转账描述
  defp build_transfer_description(:in, opts) do
    from_username = Keyword.get(opts, :from_username, "未知用户")
    reason = Keyword.get(opts, :reason, "转账")
    "收到转账：来自#{from_username} - #{reason}"
  end

  defp build_transfer_description(:out, opts) do
    to_username = Keyword.get(opts, :to_username, "未知用户")
    reason = Keyword.get(opts, :reason, "转账")
    "转账支出：转给#{to_username} - #{reason}"
  end

  # 私有函数：构建管理员操作描述
  defp build_admin_description(:add, opts) do
    admin_username = Keyword.get(opts, :admin_username, "管理员")
    reason = Keyword.get(opts, :reason, "管理员增加积分")
    "管理员操作：#{admin_username}增加积分 - #{reason}"
  end

  defp build_admin_description(:subtract, opts) do
    admin_username = Keyword.get(opts, :admin_username, "管理员")
    reason = Keyword.get(opts, :reason, "管理员减少积分")
    "管理员操作：#{admin_username}减少积分 - #{reason}"
  end
end
