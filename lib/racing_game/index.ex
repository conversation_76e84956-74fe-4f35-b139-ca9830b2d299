defmodule CypridinaWeb.RacingLive.Index do
  require <PERSON>gger

  use <PERSON><PERSON>ridinaWeb, :live_view
  alias <PERSON>pridina.RacingGame.GameManager
  alias Phoenix.PubSub

  @buy_limit_price 70

  def mount(_params, session, socket) do
    if connected?(socket) do
      # :timer.send_interval(@timer_interval, self(), :tick)

      # 订阅比赛更新
      PubSub.subscribe(Cypridina.PubSub, "race_updates")
      # 如果用户已登录，订阅用户消息
      if socket.assigns[:current_user] do
        PubSub.subscribe(Cypridina.PubSub, "user:#{socket.assigns.current_user.id}")
      end
    end

    # 从session中获取当前用户
    Logger.info("Session: #{inspect(session)}")

    socket = socket |>
    assign( :user_token, session["user_token"])
    current_user = socket.assigns[:current_user]

    # 使用新的用户资产系统获取积分
    points =
      if current_user do
        Cypridina.Accounts.get_user_points(current_user.id)
      else
        0
      end

    # 获取用户的持仓数据
    stocks =
      if current_user do
        Cypridina.RacingGame.Stock.get_user_stocks!(%{user_id: current_user.id})
        |> Enum.reduce(%{}, fn stock, acc ->
          Map.put(acc, stock.racer_id, stock.quantity)
        end)
      else
        %{}
      end

    # 获取最近5场比赛数据
    recent_races = fetch_recent_races()
    # 为动物添加序号，用于图表
    animals_with_index = add_index_to_animals(Cypridina.RacingGame.GameManager.animals())

    {:ok,
     assign(socket,
       racing_game_url: Application.get_env(:cypridina, :racing_game)[:url],
       animals: animals_with_index,
       points: points,
       betting_enabled: true,
       show_bet_history: false,
       current_race: GameManager.get_current_game_info(),
       bet_history: [],
       race_status: nil,
       recent_races: recent_races,
       my_bets: %{
         "A" => 0,
         "B" => 0,
         "C" => 0,
         "D" => 0,
         "E" => 0,
         "F" => 0
       },
       my_stocks: stocks,
       # 操作弹窗相关状态
       show_action_modal: false,
       current_action: nil,
       current_animal_id: nil,
       current_animal_name: nil,
       action_amount: 1,
       current_price: 0
     )}
  end

  # 处理统一的游戏数据更新事件
  def handle_info({:bet_won, %{bet: bet, payout: payout}}, socket) do
    {:noreply, put_flash(socket, :info, "恭喜！您在#{bet.selection}上的竞猜赢得了#{payout}积分！（已扣除手续费）")}
  end

  def handle_info({:bet_lost, %{bet: bet}}, socket) do
    {:noreply, put_flash(socket, :info, "很遗憾，您在#{bet.selection}上的竞猜没有中奖。")}
  end

  def handle_info({:game_data_update, game_data}, socket) do
    case game_data.event_type do
      :new_race ->
        # 获取当前比赛信息
        game_info = GameManager.get_current_game_info()

        Logger.info("获取当前比赛信息: #{inspect(game_info.betMap)}")

        # 获取用户股票持仓
        user = socket.assigns[:current_user]
        my_stocks = get_user_stocks(user)

        # 重置下注状态
        {:noreply,
         assign(socket,
           betting_enabled: true,
           current_race: game_info,
           my_stocks: my_stocks,
           my_bets: %{
             "A" => 0,
             "B" => 0,
             "C" => 0,
             "D" => 0,
             "E" => 0,
             "F" => 0
           }
         )}

      :race_started ->
        # 比赛开始，禁止下注
        {:noreply, assign(socket, betting_enabled: false)}

      :race_ended ->
        # 比赛结束，获取最新用户积分
        user = socket.assigns[:current_user]
        points = get_user_points(user)
        recent_races = fetch_recent_races()

        # 比赛结束，重新启用交易和竞猜功能
        {:noreply,
         socket
         |> assign(:points, points)
         |> assign(:recent_races, recent_races)
         |> assign(:betting_enabled, true)}

      :force_liquidation_completed ->
        # 强制平仓完成，更新用户积分和股票持仓
        user = socket.assigns[:current_user]

        if user do
          points = Cypridina.Accounts.get_user_points(user.id)

          my_stocks = %{
            "A" => 0,
            "B" => 0,
            "C" => 0,
            "D" => 0,
            "E" => 0,
            "F" => 0
          }

          {:noreply,
           socket
           |> put_flash(:info, "系统强制平仓完成，您的股票已按市价卖出并返还积分")
           |> assign(:points, points)
           |> assign(:my_stocks, my_stocks)}
        else
          {:noreply,
           socket
           |> put_flash(:info, "系统强制平仓完成")}
        end

      :bet_placed ->
        # 有新的下注时，更新当前比赛信息（身价可能会变化）
        game_info = GameManager.get_current_game_info()
        {:noreply, assign(socket, :current_race, game_info)}

      _ ->
        {:noreply, socket}
    end
  end

  def handle_info({:race_force_ended, race}, socket) do
    # 比赛被强制结束，获取最新用户积分和比赛历史
    user = socket.assigns[:current_user]
    points = get_user_points(user)
    recent_races = fetch_recent_races()

    # 比赛强制结束，重新启用交易和竞猜功能
    {:noreply,
     socket
     |> put_flash(:info, "比赛已被管理员强制结束")
     |> assign(:points, points)
     |> assign(:recent_races, recent_races)
     |> assign(:betting_enabled, true)}
  end

  def handle_info({event, _}, socket) do
    Logger.info("未处理s事件 #{inspect(event)}")
    {:noreply, socket}
  end

  # 辅助函数：获取用户股票持仓
  defp get_user_stocks(nil), do: %{"A" => 0, "B" => 0, "C" => 0, "D" => 0, "E" => 0, "F" => 0}

  defp get_user_stocks(user) do
    try do
      stocks =
        Cypridina.RacingGame.Stock.get_user_stocks!(%{user_id: user.id})
        |> Enum.reduce(%{}, fn stock, acc ->
          Map.put(acc, stock.racer_id, stock.quantity)
        end)

      %{
        "A" => Map.get(stocks, "A", 0),
        "B" => Map.get(stocks, "B", 0),
        "C" => Map.get(stocks, "C", 0),
        "D" => Map.get(stocks, "D", 0),
        "E" => Map.get(stocks, "E", 0),
        "F" => Map.get(stocks, "F", 0)
      }
    rescue
      _ ->
        %{"A" => 0, "B" => 0, "C" => 0, "D" => 0, "E" => 0, "F" => 0}
    end
  end

  # 辅助函数：获取用户积分
  defp get_user_points(nil), do: 0

  defp get_user_points(user) do
    try do
      Cypridina.Accounts.get_user_points(user.id)
    rescue
      _ -> 0
    end
  end

  def handle_event("toggle_bet_history", _params, socket) do
    user = socket.assigns[:current_user]

    socket =
      if user && !socket.assigns.show_bet_history do
        # 获取用户下注历史
        bet_history = GameManager.get_user_bets(user.id)
        assign(socket, bet_history: bet_history)
      else
        socket
      end

    {:noreply, assign(socket, show_bet_history: !socket.assigns.show_bet_history)}
  end

  def handle_event("logout", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/sign-out")}
  end

  def handle_event("to_reset_password", _params, socket) do
    current_user = socket.assigns[:current_user]
     user = Cypridina.Accounts.User |> Ash.get!(current_user.id)
    strategy = user |> AshAuthentication.Info.strategy!(:username)
    {:ok, reset_token} =
      AshAuthentication.Strategy.Password.reset_token_for(strategy, user)

    {:noreply, push_navigate(socket, to: ~p"/password-reset/#{reset_token}")}
  end

  def handle_event("to_admin", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/backpex_admin/users")}
  end

  # 显示操作弹窗（买入/卖出/竞猜）
  def handle_event("show_action_modal", %{"action" => action, "animal" => animal_id}, socket) do
    # 清除之前的flash消息，避免重复显示
    socket = socket |> clear_flash(:info) |> clear_flash(:error)

    user = socket.assigns[:current_user]

    cond do
      !user ->
        {:noreply, put_flash(socket, :error, "请先登录后再操作！")}

      # 检查比赛是否进行中（禁止下注期间）
      !socket.assigns.betting_enabled ->
        {:noreply, put_flash(socket, :error, "比赛正在进行中，暂时无法操作！")}

      # 验证卖出时是否有足够库存
      action == "sell" && Map.get(socket.assigns.my_stocks, animal_id, 0) <= 0 ->
        {:noreply, put_flash(socket, :error, "您没有持有该选手的股票，无法卖出！")}

      true ->
        current_race = socket.assigns[:current_race]
        animal = socket.assigns.animals |> Enum.find(fn a -> a.id == animal_id end)
        # 根据操作类型设置不同的默认值
        {default_amount, max_amount} =
          case action do
            "sell" ->
              current_holding = Map.get(socket.assigns.my_stocks, animal_id, 0)
              {1, current_holding}

            "buy" ->
              max_affordable = div(socket.assigns.points, current_race.betMap[animal_id])
              {1, max_affordable}

            "bet" ->
              # 现在竞猜也是基于数量，而不是直接设置积分
              max_affordable = div(socket.assigns.points, current_race.betMap[animal_id])
              {1, max_affordable}
          end

        # 确保默认值不超过最大值
        default_amount = min(default_amount, max(1, max_amount))

        {:noreply,
         socket
         |> assign(:show_action_modal, true)
         |> assign(:current_action, action)
         |> assign(:current_animal_id, animal_id)
         |> assign(:current_animal_name, animal.name)
         |> assign(:action_amount, default_amount)
         |> assign(:current_price, current_race.betMap[animal_id])}
    end
  end

  # 更新操作数量
  def handle_event("update_action_amount", %{"value" => value}, socket) do
    {amount, _} = Integer.parse(value)
    {:noreply, assign(socket, update_action_amount(amount, socket))}
  end

  # 更新操作数量
  def handle_event("update_action_amount", params, socket) do
    # 从表单中获取值
    Logger.info(params, label: "收到的参数")
    value = params["action-amount"]

    if is_nil(value) || value == "" do
      {:noreply, socket}
    else
      {amount, _} = Integer.parse(value)
      {:noreply, assign(socket, update_action_amount(amount, socket))}
    end
  end

  # 统一处理数量更新的辅助函数
  defp update_action_amount(amount, socket) do
    amount = max(1, amount)
    # 根据操作类型限制最大值
    amount =
      case socket.assigns.current_action do
        "sell" ->
          max_amount = Map.get(socket.assigns.my_stocks, socket.assigns.current_animal_id, 0)
          min(amount, max_amount)

        "buy" ->
          max_affordable = div(socket.assigns.points, socket.assigns.current_price)
          min(amount, max_affordable)

        "bet" ->
          max_affordable = div(socket.assigns.points, socket.assigns.current_price)
          min(amount, max_affordable)

        _ ->
          amount
      end

    %{action_amount: amount}
  end

  # 减少操作数量
  def handle_event("decrease_amount", _params, socket) do
    current_amount = socket.assigns.action_amount
    new_amount = max(1, current_amount - 1)
    {:noreply, assign(socket, update_action_amount(new_amount, socket))}
  end

  # 增加操作数量
  def handle_event("increase_amount", _params, socket) do
    current_amount = socket.assigns.action_amount
    new_amount = current_amount + 1
    {:noreply, assign(socket, update_action_amount(new_amount, socket))}
  end

  # 取消操作
  def handle_event("cancel_action", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_action_modal, false)
     |> assign(:current_action, nil)
     |> assign(:current_animal_id, nil)
     |> assign(:current_animal_name, nil)}
  end

  # 确认操作
  def handle_event(
        "confirm_action",
        %{"action" => action, "animal" => animal_id, "amount" => amount_str},
        socket
      ) do
    user = socket.assigns[:current_user]
    {amount, _} = Integer.parse(amount_str)

    socket =
      case action do
        "buy" -> handle_buy_stock(socket, user, animal_id, amount)
        "sell" -> handle_sell_stock(socket, user, animal_id, amount)
        "bet" -> handle_bet(socket, user, animal_id, amount)
        _ -> socket |> put_flash(:error, "未知操作")
      end

    {:noreply,
     socket
     |> assign(:show_action_modal, false)
     |> assign(:current_action, nil)
     |> assign(:current_animal_id, nil)
     |> assign(:current_animal_name, nil)}
  end

  # 处理买入股票操作
  defp handle_buy_stock(socket, user, animal_id, amount) do
    current_race = socket.assigns[:current_race]
    animal = socket.assigns.animals |> Enum.find(fn a -> a.id == animal_id end)

    # 获取当前价格和总价
    stock_price = current_race.betMap[animal_id]
    total_price = stock_price * amount

    # 检查用户是否有足够积分
    user_points = socket.assigns.points

    cond do
      user_points < total_price ->
        socket |> put_flash(:error, "积分不足，无法购买！")

      stock_price < @buy_limit_price ->
        socket |> put_flash(:error, "当前价格低于#{inspect(@buy_limit_price)}，无法购买！")

      true ->
        # 扣除用户积分
        case Cypridina.Accounts.subtract_points(user.id, total_price) do
          {:ok, _asset} ->
            # 增加用户的股票持仓
            case Cypridina.RacingGame.Stock.add!(%{
                   user_id: user.id,
                   racer_id: animal_id,
                   amount: amount
                 }) do
              %{quantity: quantity} ->
                # 记录到股票统计
                try do
                  Cypridina.RacingGame.StockStatistics.update_on_buy!(%{
                    racer_id: animal_id,
                    quantity: amount,
                    cost_amount: Decimal.new(total_price)
                  })

                  # 广播股票统计更新事件
                  Phoenix.PubSub.broadcast(
                    Cypridina.PubSub,
                    GameManager.race_topic(),
                    {:stock_statistics_updated, %{
                      event_type: :stock_bought,
                      racer_id: animal_id,
                      quantity: amount,
                      cost_amount: total_price,
                      user_id: user.id
                    }}
                  )
                rescue
                  error ->
                    Logger.error("更新股票统计失败: #{inspect(error)}")
                end

                # 更新用户积分和持仓数据
                points = Cypridina.Accounts.get_user_points(user.id)
                my_stocks = socket.assigns.my_stocks |> Map.put(animal_id, quantity)

                socket
                |> put_flash(:info, "成功买入#{animal.name}的#{amount}份股票！")
                |> assign(:points, points)
                |> assign(:my_stocks, my_stocks)

              _ ->
                socket |> put_flash(:error, "买入失败，请稍后再试！")
            end

          {:error, reason} ->
            socket |> put_flash(:error, "积分扣除失败: #{inspect(reason)}")
        end
    end
  end

  # 处理卖出股票操作
  defp handle_sell_stock(socket, user, animal_id, amount) do
    current_race = socket.assigns[:current_race]
    animal = socket.assigns.animals |> Enum.find(fn a -> a.id == animal_id end)

    # 检查用户是否有足够的持仓
    current_holding = Map.get(socket.assigns.my_stocks, animal_id, 0)

    if current_holding < amount do
      socket |> put_flash(:error, "您的#{animal.name}持仓不足，无法卖出！")
    else
      # 获取当前价格和总价
      stock_price = current_race.betMap[animal_id]
      total_price = stock_price * amount

      case Cypridina.RacingGame.Stock.subtract!(%{
             user_id: user.id,
             racer_id: animal_id,
             amount: amount
           }) do
        %{quantity: quantity} ->
          # 增加用户积分
          case Cypridina.Accounts.add_points(user.id, total_price) do
            {:ok, _asset} ->
              # 记录到股票统计
              try do
                Cypridina.RacingGame.StockStatistics.update_on_sell!(%{
                  racer_id: animal_id,
                  quantity: amount,
                  sell_amount: Decimal.new(total_price)
                })

                # 广播股票统计更新事件
                Phoenix.PubSub.broadcast(
                  Cypridina.PubSub,
                  GameManager.race_topic(),
                  {:stock_statistics_updated, %{
                    event_type: :stock_sold,
                    racer_id: animal_id,
                    quantity: amount,
                    sell_amount: total_price,
                    user_id: user.id
                  }}
                )
              rescue
                error ->
                  Logger.error("更新股票统计失败: #{inspect(error)}")
              end

              # 更新用户积分和持仓数据
              points = Cypridina.Accounts.get_user_points(user.id)
              my_stocks = socket.assigns.my_stocks |> Map.put(animal_id, quantity)

              socket
              |> put_flash(:info, "成功卖出#{animal.name}的#{amount}份股票，获得#{total_price}积分！")
              |> assign(:points, points)
              |> assign(:my_stocks, my_stocks)

            {:error, reason} ->
              socket |> put_flash(:error, "积分增加失败: #{inspect(reason)}")
          end

        reason ->
          Logger.info("卖出失败 #{inspect(reason)}")
          socket |> put_flash(:error, "卖出失败，请稍后再试！")
      end
    end
  end

  # 处理竞猜操作
  defp handle_bet(socket, user, animal_id, quantity) do
    current_race = socket.assigns[:current_race]
    my_bets = socket.assigns[:my_bets]

    # 计算下注所需积分：身价 × 数量
    bet_price = current_race.betMap[animal_id]
    total_bet_amount = bet_price * quantity

    # 检查用户是否有足够积分
    user_points = socket.assigns.points

    if user_points < total_bet_amount do
      socket |> put_flash(:error, "积分不足，无法下注！")
    else
      case GameManager.place_bet(user.id, animal_id, total_bet_amount) do
        {:ok, _result} ->
          # 更新用户下注记录 (记录总积分)
          my_bets = my_bets |> Map.update!(animal_id, &(&1 + total_bet_amount))

          # 更新用户积分
          points = Cypridina.Accounts.get_user_points(user.id)

          socket
          |> put_flash(:info, "成功购买#{quantity}份竞猜，共扣除#{total_bet_amount}积分！")
          |> assign(:points, points)
          |> assign(:my_bets, my_bets)

        {:error, reason} ->
          socket |> put_flash(:error, "下注失败: #{inspect(reason)}")
      end
    end
  end

  # 助手函数，为动物添加索引属性，用于K线图
  defp add_index_to_animals(animals) do
    animals
    |> Enum.with_index(1)
    |> Enum.map(fn {animal, index} ->
      Map.put(animal, :index, index)
    end)
  end

  # 模拟获取比赛历史数据
  defp fetch_race_history(date, page) do
    # 这里简单模拟一些数据，实际应用中应从数据库获取
    # date_without_dash = String.replace(date, "-", "")

    # 每页10条记录
    # start = (page - 1) * 10 + 1
    races = GameManager.get_recent_race_results()

    records =
      Enum.map(races, fn race ->
        %{
          issue: race.issue,
          bet_amount_map: race.bet_amount_map
        }
      end)

    records
  end

  # 获取最近5场比赛数据（用于排名走势图）
  defp fetch_recent_races() do
    races =
      GameManager.get_recent_race_results()
      |> Enum.map(fn race ->
        %{
          issue: race.issue,
          bet_amount_map: race.bet_amount_map
        }
      end)

    races
    |> Enum.take(10)
    |> Enum.reverse()
  end

  # 防止表单默认提交
  def handle_event("prevent_default", _params, socket) do
    {:noreply, socket}
  end
end
