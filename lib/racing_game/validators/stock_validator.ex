defmodule Cypridina.RacingGame.StockValidator do
  @moduledoc """
  验证模块，用于验证库存相关操作
  """

  @doc """
  验证库存减少操作不会导致数量变为负数
  """
  def validate_non_negative_quantity(changeset, _context) do
    case Ash.Changeset.get_attribute(changeset, :quantity) do
      nil ->
        # 新记录情况，需要看默认值或参数
        case Ash.Changeset.fetch_argument(changeset, :amount) do
          {:ok, amount} when amount <= 0 ->
            {:ok, changeset}

          {:ok, _} ->
            Ash.Changeset.add_error(changeset, :quantity, "cannot be negative")

          _ ->
            {:ok, changeset}
        end

      quantity ->
        # 现有记录情况，检查减法后是否为负
        case Ash.Changeset.fetch_argument(changeset, :amount) do
          {:ok, amount} when quantity - amount >= 0 ->
            {:ok, changeset}

          {:ok, _} ->
            Ash.Changeset.add_error(changeset, :quantity, "cannot be negative")

          _ ->
            {:ok, changeset}
        end
    end
  end
end
