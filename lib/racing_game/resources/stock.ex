defmodule Cypridina.RacingGame.Stock do
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.RacingGame

  postgres do
    table "racing_game_stocks"
    repo Cypridina.Repo
  end

  code_interface do
    define :add
    define :subtract
    define :get_user_stocks
    define :get_stock
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    create :add do
      accept [:user_id, :racer_id]
      upsert? true
      upsert_identity :unique_user_stock
      argument :amount, :integer, allow_nil?: false

      #创建走这里
      change set_attribute(:quantity, arg(:amount))
      #更新走这里
      change atomic_update(:quantity, expr(quantity + ^arg(:amount)))

      # 在 upsert 时设置初始值
      # change set_attribute(:quantity, 0)
      # 然后使用 atomic update
      # change atomic_update(:quantity, expr(expr(quantity || 0) + ^arg(:amount)))
    end

    create :subtract do
      accept [:user_id, :racer_id]
      upsert? true
      upsert_identity :unique_user_stock
      argument :amount, :integer, allow_nil?: false
      change atomic_update(:quantity, expr(quantity - ^arg(:amount)))
    end

    read :get_user_stocks do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :get_stock do
      argument :user_id, :uuid, allow_nil?: false
      argument :racer_id, :string, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and racer_id == ^arg(:racer_id))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
    end

    attribute :racer_id, :string do
      allow_nil? false
    end

    attribute :quantity, :integer do
      allow_nil? false
      default 0
      constraints min: 0
    end

    timestamps()
  end

  identities do
    identity :unique_user_stock, [:user_id, :racer_id]
  end
end
