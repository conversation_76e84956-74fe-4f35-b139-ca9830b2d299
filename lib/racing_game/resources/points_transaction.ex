defmodule Cypridina.RacingGame.PointsTransaction do
  @moduledoc """
  积分变动记录模型 - 记录所有积分变动的详细信息
  """
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.RacingGame

  postgres do
    table "racing_game_points_transactions"
    repo Cypridina.Repo
  end

  # 变动类型枚举
  @transaction_types [
    :buy_stock,      # 买入股票
    :sell_stock,     # 卖出股票
    :place_bet,      # 投注
    :win_prize,      # 获奖
    :commission,     # 抽水
    :transfer_in,    # 转账收入
    :transfer_out,   # 转账支出
    :refund,         # 退费
    :system_adjust,  # 系统调整
    :admin_add,      # 管理员增加积分
    :admin_subtract, # 管理员减少积分
    :manual_add,     # 手动增加积分
    :manual_subtract # 手动减少积分
  ]

  # 生成交易流水号
  def generate_transaction_id do
    timestamp = DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    random = :rand.uniform(9999) |> Integer.to_string() |> String.pad_leading(4, "0")
    "TXN#{timestamp}#{random}"
  end

  code_interface do
    define :create_transaction, action: :create
    define :get_user_transactions, action: :get_user_transactions
    define :get_transactions_by_type, action: :get_transactions_by_type
    define :get_transactions_by_race, action: :get_transactions_by_race
    define :get_user_balance_history, action: :get_user_balance_history
    define :get_all_transactions, action: :read
  end

  actions do
    defaults [:read, :update, :destroy]

    # 创建积分变动记录
    create :create do
      accept [
        :user_id, :transaction_type, :amount, :balance_before, :balance_after,
        :race_issue, :description, :extra_data
      ]

      change fn changeset, _context ->
        transaction_id = generate_transaction_id()
        Ash.Changeset.change_attribute(changeset, :transaction_id, transaction_id)
      end
    end

    # 根据用户ID查询交易记录
    read :get_user_transactions do
      argument :user_id, :uuid, allow_nil?: false
      argument :limit, :integer, default: 50
      argument :offset, :integer, default: 0

      filter expr(user_id == ^arg(:user_id))
      pagination offset?: true, default_limit: 50
    end

    # 根据交易类型查询
    read :get_transactions_by_type do
      argument :transaction_type, :atom, allow_nil?: false
      argument :limit, :integer, default: 50

      filter expr(transaction_type == ^arg(:transaction_type))
      pagination offset?: true, default_limit: 50
    end

    # 根据期号查询交易记录
    read :get_transactions_by_race do
      argument :race_issue, :string, allow_nil?: false

      filter expr(race_issue == ^arg(:race_issue))
    end

    # 获取用户余额历史
    read :get_user_balance_history do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true

      filter expr(user_id == ^arg(:user_id))

      filter expr(
        if not is_nil(^arg(:start_date)) do
          fragment("DATE(?)", created_at) >= ^arg(:start_date)
        else
          true
        end
      )

      filter expr(
        if not is_nil(^arg(:end_date)) do
          fragment("DATE(?)", created_at) <= ^arg(:end_date)
        else
          true
        end
      )
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :transaction_id, :string do
      allow_nil? false
      description "交易流水号"
    end

    attribute :user_id, :uuid do
      allow_nil? false
      description "用户ID"
    end

    attribute :transaction_type, :atom do
      allow_nil? false
      constraints one_of: @transaction_types
      description "变动类型"
    end

    attribute :amount, :decimal do
      allow_nil? false
      description "变动金额（正数为收入，负数为支出）"
    end

    attribute :balance_before, :decimal do
      allow_nil? false
      description "变动前余额"
    end

    attribute :balance_after, :decimal do
      allow_nil? false
      description "变动后余额"
    end

    attribute :race_issue, :string do
      allow_nil? true
      description "关联的期号（如果适用）"
    end

    attribute :description, :string do
      allow_nil? true
      description "变动描述"
    end

    attribute :extra_data, :map do
      allow_nil? true
      description "额外数据（JSON格式）"
    end

    timestamps()
  end

  # 数据验证
  validations do
    validate present([:user_id, :transaction_type, :amount, :balance_before, :balance_after]),
      message: "必填字段不能为空"
  end
end
