defmodule Cypridina.RacingGame.UserStockStatistics do
  @moduledoc """
  用户股票统计数据模型 - 跟踪每个用户的股票交易统计
  """
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.RacingGame

  postgres do
    table "racing_game_user_stock_statistics"
    repo Cypridina.Repo
  end

  code_interface do
    define :create, action: :create
    define :update, action: :update
    define :get_by_user_id, action: :get_by_user_id
    define :list, action: :read
  end

  actions do
    defaults [:read, :destroy]

    # 创建用户统计
    create :create do
      accept [
        :user_id,
        :total_points,
        :total_wins,
        :total_losses,
        :win_rate,
        :biggest_win,
        :biggest_loss,
        :average_bet,
        :total_races,
        :profit_loss
      ]
    end

    # 更新用户统计
    update :update do
      accept [
        :total_points,
        :total_wins,
        :total_losses,
        :win_rate,
        :biggest_win,
        :biggest_loss,
        :average_bet,
        :total_races,
        :profit_loss
      ]
    end

    # 根据用户ID查询统计
    read :get_by_user_id do
      argument :user_id, :string, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      get? true
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :string do
      allow_nil? false
      description "用户ID"
    end

    attribute :total_points, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "总积分"
    end

    attribute :total_wins, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "总胜利次数"
    end

    attribute :total_losses, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "总失败次数"
    end

    attribute :win_rate, :decimal do
      allow_nil? false
      default 0.0
      constraints min: 0, max: 100
      description "胜率百分比"
    end

    attribute :biggest_win, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "最大单次胜利"
    end

    attribute :biggest_loss, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "最大单次失败"
    end

    attribute :average_bet, :decimal do
      allow_nil? false
      default 0.0
      constraints min: 0
      description "平均下注金额"
    end

    attribute :total_races, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "总参与比赛次数"
    end

    attribute :profit_loss, :integer do
      allow_nil? false
      default 0
      description "盈亏总额"
    end

    timestamps()
  end

  identities do
    identity :unique_user_stats, [:user_id]
  end

  # 数据验证
  validations do
    validate present([:user_id]),
      message: "用户ID不能为空"
  end
end
