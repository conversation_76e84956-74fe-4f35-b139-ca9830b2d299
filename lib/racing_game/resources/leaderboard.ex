defmodule <PERSON>p<PERSON><PERSON>.RacingGame.Leaderboard do
  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.RacingGame,
    data_layer: AshPostgres.DataLayer

  require Logger

  postgres do
    table "leaderboards"
    repo Cy<PERSON><PERSON><PERSON>.Repo
  end

  code_interface do
    define :get_by_racer_number, args: [:racer_number], action: :by_racer_number
    define :increment_win, action: :increment_win
    define :list_ranked, action: :list_ranked
  end

  actions do
    defaults [:read, :destroy, create: :*]

    update :update do
      primary? true
      accept [:win_count]
    end

    update :increment_win do
      accept []
      change set_attribute(:win_count, expr(win_count + 1))
    end

    read :by_racer_number do
      argument :racer_number, :integer do
        allow_nil? false
      end

      filter expr(racer_number == ^arg(:racer_number))
    end

    read :list_ranked do
      prepare build(sort: [win_count: :desc])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :racer_number, :integer do
      allow_nil? false
    end

    attribute :win_count, :integer
    timestamps()
  end
end
