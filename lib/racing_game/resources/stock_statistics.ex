defmodule Cypridina.RacingGame.StockStatistics do
  @moduledoc """
  股票统计数据模型 - 跟踪每只动物的总持仓数目和成本总和
  """
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.RacingGame

  postgres do
    table "racing_game_stock_statistics"
    repo Cypridina.Repo
  end

  code_interface do
    define :update_on_buy, action: :update_on_buy
    define :update_on_sell, action: :update_on_sell
    define :get_by_racer, action: :get_by_racer
    define :get_all_statistics, action: :read
    define :reset_statistics, action: :reset_statistics
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    # 买入时更新统计
    create :update_on_buy do
      accept [:racer_id]
      upsert? true
      upsert_identity :unique_racer_stats
      argument :quantity, :integer, allow_nil?: false
      argument :cost_amount, :decimal, allow_nil?: false

      # 创建时设置初始值
      change set_attribute(:total_bought, arg(:quantity))
      change set_attribute(:total_cost, arg(:cost_amount))

      # 更新时累加买入数量和成本
      change atomic_update(:total_bought, expr(total_bought + ^arg(:quantity)))
      change atomic_update(:total_cost, expr(total_cost + ^arg(:cost_amount)))
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    # 卖出时更新统计
    create :update_on_sell do
      accept [:racer_id]
      upsert? true
      upsert_identity :unique_racer_stats
      argument :quantity, :integer, allow_nil?: false
      argument :sell_amount, :decimal, allow_nil?: false

      # 创建时设置初始值（如果是第一次卖出）
      change set_attribute(:total_sold, arg(:quantity))
      change set_attribute(:total_revenue, arg(:sell_amount))

      # 更新时累加卖出数量和收入
      change atomic_update(:total_sold, expr(total_sold + ^arg(:quantity)))
      change atomic_update(:total_revenue, expr(total_revenue + ^arg(:sell_amount)))
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    # 根据动物ID查询统计
    read :get_by_racer do
      get? true
      argument :racer_id, :string, allow_nil?: false
      filter expr(racer_id == ^arg(:racer_id))
    end

    # 重置所有统计数据
    update :reset_statistics do
      change set_attribute(:total_quantity, 0)
      change set_attribute(:total_cost, 0)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :racer_id, :string do
      allow_nil? false
      description "动物ID (A, B, C, D, E, F)"
    end

    attribute :total_bought, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "总买入数量"
    end

    attribute :total_sold, :integer do
      allow_nil? false
      default 0
      constraints min: 0
      description "总卖出数量"
    end

    attribute :total_cost, :decimal do
      allow_nil? false
      default 0
      constraints min: 0
      description "总买入成本"
    end

    attribute :total_revenue, :decimal do
      allow_nil? false
      default 0
      constraints min: 0
      description "总卖出收入"
    end

    timestamps()
  end

  identities do
    identity :unique_racer_stats, [:racer_id]
  end

  # 计算字段：平均成本
  calculations do
    calculate :average_cost, :decimal, expr(
      if total_quantity > 0 do
        total_cost / total_quantity
      else
        0
      end
    ) do
      description "平均成本 = 总成本 / 总持仓数量"
    end
  end

  # 数据验证
  validations do
    validate attribute_does_not_equal(:total_quantity, -1),
      message: "总持仓数量不能为负数"
    validate attribute_does_not_equal(:total_cost, -1),
      message: "总成本不能为负数"
    validate present([:racer_id]),
      message: "动物ID不能为空"
  end
end
