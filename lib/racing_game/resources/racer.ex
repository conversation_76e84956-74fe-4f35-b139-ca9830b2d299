defmodule <PERSON><PERSON><PERSON><PERSON>.RacingGame.Racer do
  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.RacingGame,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "racers"
    repo <PERSON><PERSON><PERSON><PERSON>.Repo
  end

  code_interface do
    define :get_by_number, action: :read, get_by: [:number]
    define :increment_wins, action: :increment_wins
    define :list_all, action: :read
    define :list_ranked, action: :list_by_wins
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:number, :code, :name, :current_odds]
    end

    update :update do
      primary? true
      accept [:total_wins, :current_odds]
    end

    update :increment_wins do
      accept []

      change set_attribute(:total_wins, expr(total_wins + 1))
    end

    destroy :destroy do
      primary? true
    end

    read :list_by_wins do
      pagination offset?: true, default_limit: 10
      prepare build(sort: :total_wins)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :number, :integer do
      allow_nil? false
    end

    attribute :code, :string do
      allow_nil? false
    end

    attribute :name, :string do
      allow_nil? false
    end

    attribute :total_wins, :integer
    attribute :current_odds, :decimal
    timestamps()
  end
end
