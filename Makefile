.PHONY: server dev setup stop logs logs-view logs-clean

# 启动所有服务器
server:
	PHX_SERVER=true iex -S mix phx.server

# 开发模式启动
dev:
	mix phx.server

# 初始设置
setup:
	mkdir -p logs
	mix deps.get
	mix ecto.create
	mix ecto.migrate
	cd apps/teen && npm install --prefix assets
	cd apps/racing && npm install --prefix assets

# 重置数据库
reset:
	mix ecto.drop
	mix ecto.create
	mix ecto.migrate

# 运行测试
test:
	mix test

# 停止Phoenix服务
stop:
	# pkill -f "beam.smp.*phx.server\|beam.smp.*PHX_SERVER" || true
	# pikill -f "mix phx.server"
	pkill -f "beam.smp"

# 创建日志目录
logs:
	mkdir -p logs

# 查看日志文件
logs-view:
	@echo "=== System Log ==="
	@tail -n 20 logs/system.log 2>/dev/null || echo "No system log found"
	@echo "\n=== Cypridina Log ==="
	@tail -n 20 logs/cypridina.log 2>/dev/null || echo "No cypridina log found"
	@echo "\n=== Racing Log ==="
	@tail -n 20 logs/racing.log 2>/dev/null || echo "No racing log found"
	@echo "\n=== Teen Log ==="
	@tail -n 20 logs/teen.log 2>/dev/null || echo "No teen log found"

# 清理日志文件
logs-clean:
	rm -f logs/*.log*